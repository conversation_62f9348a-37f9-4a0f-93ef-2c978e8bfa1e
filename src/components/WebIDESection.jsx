import React, { useState, useEffect, useRef } from 'react';
import { FaCode, FaExternalLinkAlt, FaSync, FaExpand, FaCompress } from 'react-icons/fa';

const WebIDESection = ({ user, onLogin }) => {
  const [isIframeLoaded, setIsIframeLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const iframeRef = useRef(null);
  const sectionRef = useRef(null);

  // 从环境变量获取WebIDE配置
  const getWebIDEConfig = () => {
    const baseUrl = import.meta.env.VITE_WEBIDE_BASE_URL || 'http://192.168.1.3:3667';
    const token = import.meta.env.VITE_WEBIDE_TOKEN || 'tk-ynnx-llm';
    const tokenParam = import.meta.env.VITE_WEBIDE_TOKEN_PARAM || 'tkn';
    
    // 支持多种token参数格式
    const url = `${baseUrl}?${tokenParam}=${token}`;
    
    console.log('WebIDE配置:', { baseUrl, token, tokenParam, url });
    return { baseUrl, url, token, tokenParam };
  };

  const { baseUrl, url, token, tokenParam } = getWebIDEConfig();

  // 检查用户是否已登录
  const isAuthenticated = user && user.name;

  // 处理iframe加载
  const handleIframeLoad = () => {
    const currentSrc = iframeRef.current?.src || 'unknown';
    console.log('iframe加载完成，当前src:', currentSrc);
    
    try {
      // 尝试访问iframe的contentDocument来检查是否真正加载完成
      const iframeDoc = iframeRef.current?.contentDocument;
      if (iframeDoc && iframeDoc.readyState === 'complete') {
        console.log('iframe文档加载完成，readyState:', iframeDoc.readyState);
        
        // 检查是否是OpenVSCode的页面
        if (currentSrc.includes('192.168.1.3:3667') || 
            iframeDoc.title.includes('VSCode') || 
            iframeDoc.body?.innerHTML.includes('vscode')) {
          console.log('检测到OpenVSCode内容，WebIDE加载成功');
          setIsIframeLoaded(true);
          setIsLoading(false);
          setError(null);
          return;
        }
      }
    } catch (e) {
      // 跨域访问限制，这是正常的
      console.log('无法访问iframe内容（跨域限制）:', e.message);
    }
    
    // 只有当加载的不是about:blank时才认为真正加载完成
    if (currentSrc !== 'about:blank' && !currentSrc.includes('about:blank')) {
      console.log('WebIDE iframe加载完成，假设内容正确');
      setIsIframeLoaded(true);
      setIsLoading(false);
      setError(null);
    } else {
      console.log('iframe加载了about:blank，等待实际内容加载');
    }
  };

  // 处理iframe加载错误
  const handleIframeError = (event) => {
    console.error('WebIDE iframe 加载错误:', event);
    console.error('URL:', url, 'baseUrl:', baseUrl);
    setIsIframeLoaded(false);
    setIsLoading(false);
    setError('无法连接到WebIDE服务，请检查服务器状态');
  };

  // 直接加载iframe - 将函数提前定义
  const loadIframe = () => {
    console.log('🚀 开始加载iframe，URL:', url);
    console.log('📋 配置信息:', { baseUrl, token, tokenParam });
    
    if (iframeRef.current) {
      setIsLoading(true);
      setError(null);
      setIsIframeLoaded(false);
      
      console.log('🔄 设置iframe src:', url);
      iframeRef.current.src = url;
      
      // 监听iframe的unload事件（可能表示重定向）
      iframeRef.current.onload = (_event) => {
        console.log('📡 iframe onload事件触发');
        handleIframeLoad();
      };
      
      // 设置加载超时（30秒）
      setTimeout(() => {
        if (isLoading && !isIframeLoaded) {
          console.warn('⏰ iframe加载超时');
          setError('加载超时，请尝试刷新或在新窗口中打开。如果新窗口能正常打开，说明可能是iframe限制问题。');
          setIsLoading(false);
        }
      }, 30000);
      
      // 短时间后检查加载状态
      setTimeout(() => {
        if (iframeRef.current) {
          const currentSrc = iframeRef.current.src;
          console.log('🔍 5秒后检查iframe状态，当前src:', currentSrc);
        }
      }, 5000);
    } else {
      console.error('❌ iframe ref不可用');
    }
  };



  // 刷新iframe
  const refreshIframe = () => {
    if (iframeRef.current) {
      setIsLoading(true);
      setError(null);
      
      // 直接重新加载iframe
      loadIframe();
    }
  };

  // 切换全屏模式
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      if (sectionRef.current && sectionRef.current.requestFullscreen) {
        sectionRef.current.requestFullscreen();
        setIsFullscreen(true);
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
        setIsFullscreen(false);
      }
    }
  };

  // 监听全屏变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 组件加载时加载iframe
  useEffect(() => {
    if (isAuthenticated && iframeRef.current) {
      console.log('WebIDE组件加载，开始加载iframe');
      // 稍微延迟一点确保iframe已经准备好
      setTimeout(() => {
        loadIframe();
      }, 100);
    }
  }, [isAuthenticated, url]);

  // 监听iframe消息和错误处理
  useEffect(() => {
    const handleMessage = (event) => {
      // 提取origin用于比较
      const serverOrigin = new URL(baseUrl).origin;

      // 只处理来自OpenVSCode服务器的消息
      if (event.origin === serverOrigin) {
        console.log('收到OpenVSCode消息:', event.data);

        // 处理VS Code的特殊消息
        if (event.data && typeof event.data === 'object') {
          // 忽略生命周期相关的错误消息
          if (event.data.type === 'lifecycle-error' ||
              event.data.message?.includes('Long running operations during shutdown')) {
            console.log('忽略VS Code生命周期错误（正常行为）');
            return;
          }
        }
      } else {
        console.log('收到其他来源消息:', event.origin, event.data);
      }
    };

    // 全局错误处理，过滤VS Code相关的已知错误
    const handleGlobalError = (event) => {
      const errorMessage = event.message || event.error?.message || '';

      // 过滤已知的VS Code Web错误
      const knownErrors = [
        'Long running operations during shutdown are unsupported in the web',
        'No active subscription for controller',
        'vsda_bg.wasm',
        'vsda.js',
        'ERR_ABORTED 404'
      ];

      if (knownErrors.some(known => errorMessage.includes(known))) {
        console.log('已过滤VS Code已知错误:', errorMessage);
        event.preventDefault();
        return false;
      }
    };

    window.addEventListener('message', handleMessage);
    window.addEventListener('error', handleGlobalError);

    return () => {
      window.removeEventListener('message', handleMessage);
      window.removeEventListener('error', handleGlobalError);
    };
  }, [baseUrl]);

  // 在新窗口打开IDE
  const openInNewWindow = () => {
    // 移除窗口大小限制，在新标签页中打开，避免弹出窗口的安全限制
    window.open(url, '_blank');
  };

  if (!isAuthenticated) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(circle_500px_at_50%_200px,rgba(0,255,255,0.1),transparent)]"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full mb-6 animate-pulse">
              <FaCode className="text-2xl text-white" />
            </div>
            <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
              Web IDE
            </h2>
            <p className="text-gray-300 text-lg mb-8 max-w-2xl mx-auto">
              基于 OpenVSCode 的云端开发环境，支持多种编程语言，提供完整的代码编辑、调试和项目管理功能。
            </p>
            <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-6 mb-8 max-w-md mx-auto">
              <p className="text-yellow-400 font-semibold">需要登录才能访问</p>
              <p className="text-yellow-300 text-sm mt-2">
                Web IDE 是高级功能，需要验证用户身份
              </p>
            </div>
            <button
              onClick={onLogin}
              className="bg-gradient-to-r from-cyan-400 to-blue-500 text-black px-8 py-4 rounded-lg font-bold text-lg shadow-lg hover:scale-105 transition-transform duration-200 inline-flex items-center gap-2"
            >
              <FaCode />
              立即登录使用
            </button>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section 
      ref={sectionRef}
      className={`py-20 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden ${isFullscreen ? 'fixed inset-0 z-50 py-0' : ''}`}
    >
      <div className="absolute inset-0 bg-[radial-gradient(circle_500px_at_50%_200px,rgba(0,255,255,0.1),transparent)]"></div>
      
      <div className={`container mx-auto px-4 relative z-10 ${isFullscreen ? 'max-w-none h-full' : ''}`}>
        {/* 标题和控制按钮 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full mb-6 animate-pulse">
            <FaCode className="text-2xl text-white" />
          </div>
          <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
            Web IDE
          </h2>
          <p className="text-gray-300 text-lg mb-8 max-w-2xl mx-auto">
            基于 OpenVSCode 的云端开发环境，支持多种编程语言，提供完整的代码编辑、调试和项目管理功能。
          </p>
          
          {/* 调试信息 */}
          <div className="bg-gray-800/50 rounded-lg p-4 mb-6 text-sm">
            <p className="text-cyan-400 font-semibold mb-2">🔧 配置信息：</p>
            <div className="text-gray-300 space-y-1">
              <p>• 服务器地址: {baseUrl}</p>
              <p>• 完整URL: {url}</p>
              <p>• iframe状态: {isIframeLoaded ? '已加载' : '未加载'}</p>
              <p>• 加载状态: {isLoading ? '加载中...' : '就绪'}</p>
            </div>
          </div>

          {/* 控制按钮 */}
          <div className="flex justify-center gap-4 mb-8">
            <button
              onClick={refreshIframe}
              disabled={isLoading}
              className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2 disabled:opacity-50"
            >
              <FaSync className={isLoading ? 'animate-spin' : ''} />
              刷新
            </button>
            
            <button
              onClick={toggleFullscreen}
              className="bg-green-500/20 hover:bg-green-500/30 text-green-400 px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
            >
              {isFullscreen ? <FaCompress /> : <FaExpand />}
              {isFullscreen ? '退出全屏' : '全屏'}
            </button>
            
            <button
              onClick={openInNewWindow}
              className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-400 px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
            >
              <FaExternalLinkAlt />
              新窗口打开
            </button>
            
            <button
              onClick={() => {
                console.log('测试WebIDE URL:', url);
                window.open(url, '_blank');
              }}
              className="bg-orange-500/20 hover:bg-orange-500/30 text-orange-400 px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
            >
              <FaCode />
              测试URL
            </button>
          </div>
        </div>

        {/* IDE容器 */}
        <div className={`relative bg-gray-800 rounded-lg overflow-hidden shadow-2xl ${isFullscreen ? 'h-full' : 'h-[800px]'}`}>
          {/* 加载状态 */}
          {isLoading && (
            <div className="absolute inset-0 bg-gray-800 flex items-center justify-center z-10">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto mb-4"></div>
                <p className="text-white text-lg">正在加载 Web IDE...</p>
                <p className="text-gray-400 text-sm mt-2">连接地址: {baseUrl}</p>
              </div>
            </div>
          )}

          {/* 错误状态 */}
          {error && (
            <div className="absolute inset-0 bg-gray-800 flex items-center justify-center z-10">
              <div className="text-center max-w-md">
                <div className="text-red-400 text-6xl mb-4">⚠️</div>
                <p className="text-white text-lg mb-2">{error}</p>
                <p className="text-gray-400 text-sm mb-2">目标地址: {baseUrl}</p>
                <p className="text-gray-400 text-xs mb-4">认证参数: {tokenParam}={token}</p>
                
                <div className="bg-gray-700/50 rounded-lg p-4 mb-4 text-left">
                  <p className="text-yellow-400 text-sm font-semibold mb-2">🔍 故障排除建议：</p>
                  <ul className="text-gray-300 text-xs space-y-1">
                    <li>• 确认OpenVSCode服务器正在运行</li>
                    <li>• 检查token配置是否正确</li>
                    <li>• 尝试在新窗口中直接打开URL</li>
                    <li>• 查看浏览器控制台的详细错误信息</li>
                  </ul>
                </div>
                
                <div className="flex gap-2 justify-center">
                  <button
                    onClick={refreshIframe}
                    className="bg-cyan-500/20 hover:bg-cyan-500/30 text-cyan-400 px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
                  >
                    <FaSync />
                    重试连接
                  </button>
                  <button
                    onClick={() => window.open(url, '_blank')}
                    className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-400 px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
                  >
                    <FaExternalLinkAlt />
                    新窗口测试
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* iframe容器 */}
          <iframe
            ref={iframeRef}
            src="about:blank"
            className="w-full h-full border-0"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
            title="Web IDE"
            allow="fullscreen; clipboard-read; clipboard-write; cross-origin-isolated"
            referrerPolicy="no-referrer-when-downgrade"
            style={{ display: isIframeLoaded ? 'block' : 'none' }}
          />
        </div>

        {/* 使用说明 */}
        {!isFullscreen && (
          <div className="mt-8 text-center">
            <div className="bg-gray-800/50 rounded-lg p-6 max-w-4xl mx-auto">
              <h3 className="text-xl font-bold text-white mb-4">使用说明</h3>
              <div className="grid md:grid-cols-3 gap-4 text-sm text-gray-300">
                <div>
                  <h4 className="font-semibold text-cyan-400 mb-2">📁 文件管理</h4>
                  <p>支持创建、编辑、删除文件和文件夹，提供完整的文件系统操作</p>
                </div>
                <div>
                  <h4 className="font-semibold text-cyan-400 mb-2">🔍 智能提示</h4>
                  <p>内置语法高亮、代码补全、错误检查等智能开发辅助功能</p>
                </div>
                <div>
                  <h4 className="font-semibold text-cyan-400 mb-2">🔧 扩展支持</h4>
                  <p>支持安装VS Code扩展，自定义开发环境和工作流程</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default WebIDESection; 