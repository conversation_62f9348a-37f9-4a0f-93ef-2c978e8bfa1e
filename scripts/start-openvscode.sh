#!/bin/bash

echo "🚀 启动OpenVSCode服务器"
echo "========================"

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 停止现有的OpenVSCode容器（如果存在）
if docker ps -a --format "table {{.Names}}" | grep -q "ynnx-openvscode"; then
    echo "🛑 停止现有的OpenVSCode容器..."
    docker stop ynnx-openvscode >/dev/null 2>&1
    docker rm ynnx-openvscode >/dev/null 2>&1
fi

# 创建工作空间目录
WORKSPACE_DIR="./workspace"
if [ ! -d "$WORKSPACE_DIR" ]; then
    echo "📁 创建工作空间目录: $WORKSPACE_DIR"
    mkdir -p "$WORKSPACE_DIR"
fi

echo "🔧 启动OpenVSCode服务器..."
echo "   - 端口: 3667"
echo "   - Token: tk-ynnx-llm"
echo "   - 工作空间: $WORKSPACE_DIR"

# 启动OpenVSCode服务器
docker run -d \
    --name ynnx-openvscode \
    --restart unless-stopped \
    -p 3667:3000 \
    -e CONNECTION_TOKEN=tk-ynnx-llm \
    -e OPENVSCODE_SERVER_ROOT=/home/<USER>
    -e VSCODE_PROXY_URI=http://localhost:3668 \
    -v "$(pwd)/$WORKSPACE_DIR:/home/<USER>" \
    gitpod/openvscode-server:latest \
    --host 0.0.0.0 \
    --port 3000 \
    --connection-token tk-ynnx-llm \
    --without-connection-token \
    --disable-telemetry \
    --disable-update-check

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ OpenVSCode服务器启动成功!"
    echo ""
    echo "🌐 访问地址:"
    echo "  - 直接访问: http://***********:3667?tkn=tk-ynnx-llm"
    echo "  - 代理访问: http://localhost:3668?tkn=tk-ynnx-llm"
    echo ""
    echo "📋 管理命令:"
    echo "  - 查看日志: docker logs -f ynnx-openvscode"
    echo "  - 停止服务: docker stop ynnx-openvscode"
    echo "  - 重启服务: docker restart ynnx-openvscode"
    echo ""
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 5
    
    # 检查服务状态
    if curl -s --connect-timeout 5 "http://***********:3667" >/dev/null; then
        echo "✅ 服务健康检查通过"
    else
        echo "⚠️  服务可能还在启动中，请稍等片刻"
    fi
else
    echo "❌ OpenVSCode服务器启动失败"
    exit 1
fi
