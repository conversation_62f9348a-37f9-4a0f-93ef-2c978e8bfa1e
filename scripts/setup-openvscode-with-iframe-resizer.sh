#!/bin/bash

echo "🚀 配置OpenVSCode与iframe-resizer集成"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo ""
echo -e "${BLUE}📋 检查依赖${NC}"
echo "================================"

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
    exit 1
fi

# 检查npm依赖
if ! npm list iframe-resizer-react >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  iframe-resizer-react未安装，正在安装...${NC}"
    npm install iframe-resizer-react iframe-resizer
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ iframe-resizer-react安装成功${NC}"
    else
        echo -e "${RED}❌ iframe-resizer-react安装失败${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ iframe-resizer-react已安装${NC}"
fi

echo ""
echo -e "${BLUE}🔧 配置OpenVSCode服务器${NC}"
echo "================================"

# 停止现有的OpenVSCode容器
if docker ps -a --format "table {{.Names}}" | grep -q "ynnx-openvscode"; then
    echo "🛑 停止现有的OpenVSCode容器..."
    docker stop ynnx-openvscode >/dev/null 2>&1
    docker rm ynnx-openvscode >/dev/null 2>&1
fi

# 创建工作空间目录
WORKSPACE_DIR="./workspace"
if [ ! -d "$WORKSPACE_DIR" ]; then
    echo "📁 创建工作空间目录: $WORKSPACE_DIR"
    mkdir -p "$WORKSPACE_DIR"
fi

# 创建iframe-resizer配置文件
echo "📝 创建iframe-resizer配置文件..."
cat > "$WORKSPACE_DIR/iframe-resizer-config.js" << 'EOF'
// iframe-resizer配置文件
// 这个文件将被注入到OpenVSCode中以支持iframe-resizer

(function() {
    'use strict';
    
    console.log('🔧 初始化iframe-resizer支持...');
    
    // 检查是否在iframe中
    if (window.parent !== window) {
        console.log('✅ 检测到iframe环境');
        
        // 动态加载iframe-resizer的子页面脚本
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/iframe-resizer@4.3.7/js/iframeResizer.contentWindow.min.js';
        script.onload = function() {
            console.log('✅ iframe-resizer子页面脚本加载成功');
            
            // 发送准备就绪消息
            if (window.parentIFrame) {
                window.parentIFrame.sendMessage({
                    type: 'vscode-ready',
                    timestamp: Date.now()
                });
            }
        };
        script.onerror = function() {
            console.error('❌ iframe-resizer子页面脚本加载失败');
        };
        document.head.appendChild(script);
        
        // 监听VS Code的加载完成事件
        const checkVSCodeReady = () => {
            // 检查VS Code是否已加载
            if (document.querySelector('.monaco-workbench') || 
                document.querySelector('.vs') ||
                document.title.includes('Visual Studio Code')) {
                console.log('🎉 VS Code已加载完成');
                
                // 通知父页面VS Code已准备就绪
                if (window.parentIFrame) {
                    window.parentIFrame.sendMessage({
                        type: 'vscode-loaded',
                        timestamp: Date.now()
                    });
                }
                
                return true;
            }
            return false;
        };
        
        // 定期检查VS Code加载状态
        const checkInterval = setInterval(() => {
            if (checkVSCodeReady()) {
                clearInterval(checkInterval);
            }
        }, 1000);
        
        // 10秒后停止检查
        setTimeout(() => {
            clearInterval(checkInterval);
        }, 10000);
        
    } else {
        console.log('ℹ️  不在iframe环境中');
    }
})();
EOF

echo -e "${GREEN}✅ iframe-resizer配置文件已创建${NC}"

echo ""
echo -e "${BLUE}🚀 启动增强版OpenVSCode服务器${NC}"
echo "================================"

echo "🔧 启动OpenVSCode服务器..."
echo "   - 端口: 3667"
echo "   - Token: tk-ynnx-llm"
echo "   - 工作空间: $WORKSPACE_DIR"
echo "   - iframe-resizer支持: 已启用"

# 启动OpenVSCode服务器
docker run -d \
    --name ynnx-openvscode \
    --restart unless-stopped \
    -p 3667:3000 \
    -e CONNECTION_TOKEN=tk-ynnx-llm \
    -e OPENVSCODE_SERVER_ROOT=/home/<USER>
    -e VSCODE_PROXY_URI=http://localhost:3668 \
    -v "$(pwd)/$WORKSPACE_DIR:/home/<USER>" \
    gitpod/openvscode-server:latest \
    --host 0.0.0.0 \
    --port 3000 \
    --connection-token tk-ynnx-llm \
    --without-connection-token \
    --disable-telemetry \
    --disable-update-check \
    --enable-proposed-api

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ OpenVSCode服务器启动成功!${NC}"
    echo ""
    echo -e "${BLUE}🌐 访问地址:${NC}"
    echo "  - 直接访问: http://***********:3667?tkn=tk-ynnx-llm"
    echo "  - 代理访问: http://localhost:3668?tkn=tk-ynnx-llm"
    echo ""
    echo -e "${BLUE}📋 管理命令:${NC}"
    echo "  - 查看日志: docker logs -f ynnx-openvscode"
    echo "  - 停止服务: docker stop ynnx-openvscode"
    echo "  - 重启服务: docker restart ynnx-openvscode"
    echo ""
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if curl -s --connect-timeout 5 "http://***********:3667" >/dev/null; then
        echo -e "${GREEN}✅ 服务健康检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️  服务可能还在启动中，请稍等片刻${NC}"
    fi
else
    echo -e "${RED}❌ OpenVSCode服务器启动失败${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🔧 更新nginx配置以支持iframe-resizer${NC}"
echo "================================"

# 检查nginx配置是否需要更新
if ! grep -q "X-Frame-Options" nginx/conf.d/webide-proxy.conf; then
    echo "📝 更新nginx配置以支持iframe嵌入..."
    
    # 备份原配置
    cp nginx/conf.d/webide-proxy.conf nginx/conf.d/webide-proxy.conf.backup.$(date +%Y%m%d_%H%M%S)
    
    # 在主要代理配置中添加iframe支持头
    sed -i '/proxy_set_header X-Forwarded-Port/a\\n        # iframe-resizer支持\
        add_header X-Frame-Options SAMEORIGIN;\
        add_header Content-Security-Policy "frame-ancestors '\''self'\'' http://localhost:* http://127.0.0.1:* http://***********:*";\
        \
        # 支持iframe通信\
        proxy_set_header X-Forwarded-Proto $scheme;\
        proxy_set_header X-Frame-Options SAMEORIGIN;' nginx/conf.d/webide-proxy.conf
    
    echo -e "${GREEN}✅ nginx配置已更新${NC}"
    
    # 重启nginx
    echo "🔄 重启nginx..."
    docker-compose restart nginx
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ nginx重启成功${NC}"
    else
        echo -e "${RED}❌ nginx重启失败${NC}"
    fi
else
    echo -e "${GREEN}✅ nginx配置已包含iframe支持${NC}"
fi

echo ""
echo -e "${BLUE}🧪 测试iframe-resizer集成${NC}"
echo "================================"

# 测试基本连接
echo "1. 测试基本连接..."
response=$(curl -s -w "%{http_code}" "http://localhost:3668" -o /dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ 基本连接正常 (HTTP $response)${NC}"
else
    echo -e "${YELLOW}⚠️  基本连接异常 (HTTP $response)${NC}"
fi

# 测试iframe头部
echo "2. 测试iframe支持..."
headers=$(curl -s -I "http://localhost:3668" | grep -i "x-frame-options\|content-security-policy")
if [ -n "$headers" ]; then
    echo -e "${GREEN}✅ iframe支持头部已配置${NC}"
    echo "   $headers"
else
    echo -e "${YELLOW}⚠️  iframe支持头部未找到${NC}"
fi

echo ""
echo -e "${GREEN}🎉 OpenVSCode与iframe-resizer集成配置完成！${NC}"
echo ""
echo -e "${BLUE}📝 使用说明:${NC}"
echo "================================"
echo ""
echo "1. 前端集成:"
echo "   ✅ iframe-resizer-react已安装"
echo "   ✅ WebIDE组件已升级"
echo "   ✅ 自动尺寸调整已启用"
echo ""
echo "2. 服务器配置:"
echo "   ✅ OpenVSCode服务器已启动"
echo "   ✅ nginx代理已配置iframe支持"
echo "   ✅ 跨域和安全策略已优化"
echo ""
echo "3. 预期改进:"
echo "   • 更好的iframe集成体验"
echo "   • 自动尺寸调整"
echo "   • 减少跨域问题"
echo "   • 更稳定的插件页面显示"
echo "   • 更好的错误处理"
echo ""
echo "4. 下一步:"
echo "   • 重新构建前端应用: npm run build"
echo "   • 重启Docker容器: docker-compose up -d"
echo "   • 在浏览器中测试WebIDE功能"
echo ""
echo -e "${GREEN}✨ 配置完成！请重新构建应用以应用更改。${NC}"
