#!/bin/bash

echo "🚀 部署YNNX AI平台 (包含WebIDE代理)"
echo "==========================================="

# 检查docker和docker-compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 检查OpenVSCode服务器
echo ""
echo "🔍 检查OpenVSCode服务器..."
if curl -s --connect-timeout 5 "http://***********:3667" > /dev/null; then
    echo "✅ OpenVSCode服务器运行正常 (***********:3667)"
else
    echo "⚠️  OpenVSCode服务器未响应 (***********:3667)"
    echo "   请确保OpenVSCode服务器正在运行"
    echo "   启动命令示例: docker run -p 3667:3000 -e CONNECTION_TOKEN=tk-ynnx-llm gitpod/openvscode-server"
    echo ""
    read -p "是否继续部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "部署取消"
        exit 1
    fi
fi

# 创建必要的目录
echo ""
echo "📁 创建必要的目录..."
mkdir -p logs/nginx
mkdir -p dist
mkdir -p workspace

# 检查nginx配置
echo ""
echo "🔧 检查nginx配置..."
if [ ! -f "nginx/conf.d/webide-proxy.conf" ]; then
    echo "❌ nginx WebIDE代理配置缺失: nginx/conf.d/webide-proxy.conf"
    exit 1
fi

echo "✅ nginx代理配置存在"

# 检查环境配置
echo ""
echo "⚙️  检查环境配置..."
if [ ! -f ".env" ]; then
    echo "📝 创建.env文件..."
    cp env.example .env
    echo "⚠️  请编辑.env文件配置相关参数"
else
    echo "✅ .env文件存在"
fi

# 检查WebIDE配置
if grep -q "VITE_WEBIDE_BASE_URL=http://localhost:3668" .env; then
    echo "✅ WebIDE配置正确 (localhost代理)"
else
    echo "🔧 更新WebIDE配置为localhost代理..."
    sed -i 's|VITE_WEBIDE_BASE_URL=.*|VITE_WEBIDE_BASE_URL=http://localhost:3668|' .env
    echo "✅ WebIDE配置已更新"
fi

# 构建和启动服务
echo ""
echo "🔨 构建应用镜像..."
docker-compose build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

echo ""
echo "🚀 启动服务..."
docker-compose up -d

if [ $? -ne 0 ]; then
    echo "❌ 启动失败"
    exit 1
fi

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo ""
echo "🔍 服务健康检查..."

# 检查主应用
if curl -s --connect-timeout 5 "http://localhost:80" > /dev/null; then
    echo "✅ 主应用服务正常 (http://localhost:80)"
else
    echo "❌ 主应用服务异常"
fi

# 检查LDAP API
if curl -s --connect-timeout 5 "http://localhost:3002" > /dev/null; then
    echo "✅ LDAP API服务正常 (http://localhost:3002)"
else
    echo "❌ LDAP API服务异常"
fi

# 检查WebIDE代理
if curl -s --connect-timeout 5 "http://localhost:3668/health" > /dev/null; then
    echo "✅ WebIDE代理服务正常 (http://localhost:3668)"
else
    echo "⚠️  WebIDE代理服务异常，检查详情..."
    docker-compose logs nginx | tail -10
fi

# 显示服务状态
echo ""
echo "📊 服务状态:"
docker-compose ps

echo ""
echo "🌐 访问地址:"
echo "  - 主应用: http://localhost:80"
echo "  - 主应用(网络): http://***********:80"
echo "  - WebIDE代理: http://localhost:3668"
echo "  - LDAP API: http://localhost:3002"

echo ""
echo "📋 管理命令:"
echo "  - 查看日志: docker-compose logs -f"
echo "  - 停止服务: docker-compose down"
echo "  - 重启服务: docker-compose restart"
echo "  - 查看状态: docker-compose ps"

echo ""
echo "🎯 WebIDE测试:"
echo "  1. 访问主应用并登录"
echo "  2. 进入Web IDE部分"
echo "  3. 检查控制台是否有安全上下文错误"
echo "  4. 测试'新窗口打开'功能"

echo ""
echo "✨ 部署完成！" 