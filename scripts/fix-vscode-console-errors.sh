#!/bin/bash

echo "🔧 修复VS Code控制台错误"
echo "================================"

echo ""
echo "📋 错误分析:"
echo "  1. 生命周期错误: VS Code Web版本在页面卸载时的正常行为"
echo "  2. VSDA模块404: Visual Studio Data Analytics模块在Web环境中不可用"
echo "  3. 扩展市场错误: 已在之前的修复中解决"

echo ""
echo "🛠️  解决方案:"

# 1. 更新nginx配置
echo "1. 更新nginx配置以拦截VSDA模块请求..."

# 检查nginx配置是否包含VSDA拦截
if grep -q "vsda.*\.(js|wasm)" nginx/conf.d/webide-proxy.conf; then
    echo "✅ nginx配置已包含VSDA模块拦截规则"
else
    echo "❌ nginx配置缺少VSDA模块拦截规则"
    echo "正在添加..."
    
    # 备份原配置
    cp nginx/conf.d/webide-proxy.conf nginx/conf.d/webide-proxy.conf.backup.$(date +%Y%m%d_%H%M%S)
    
    # 在扩展市场拦截后添加VSDA拦截
    sed -i '/add_header Content-Type application\/json;/a\\n    # 拦截VSDA模块请求，避免404错误\
    location ~* /.*vsda.*\\.(js|wasm)$ {\
        access_log off;\
        return 404 '\''// VSDA module not available in this environment'\'';\
        add_header Content-Type application/javascript;\
    }' nginx/conf.d/webide-proxy.conf
    
    echo "✅ 已添加VSDA模块拦截规则"
fi

# 2. 重启nginx应用配置
echo ""
echo "2. 重启nginx应用配置..."
docker-compose restart nginx

if [ $? -eq 0 ]; then
    echo "✅ nginx重启成功"
else
    echo "❌ nginx重启失败"
    exit 1
fi

# 3. 测试拦截规则
echo ""
echo "3. 测试拦截规则..."

# 测试扩展市场拦截
echo "测试扩展市场拦截..."
response=$(curl -s -w "%{http_code}" "http://localhost:3668/vscode/gallery/test" -o /dev/null)
if [ "$response" = "404" ]; then
    echo "✅ 扩展市场拦截工作正常 (HTTP 404)"
else
    echo "⚠️  扩展市场拦截可能未生效 (HTTP $response)"
fi

# 测试VSDA模块拦截
echo "测试VSDA模块拦截..."
response=$(curl -s -w "%{http_code}" "http://localhost:3668/static/node_modules/vsda/rust/web/vsda_bg.wasm" -o /dev/null)
if [ "$response" = "404" ]; then
    echo "✅ VSDA模块拦截工作正常 (HTTP 404)"
else
    echo "⚠️  VSDA模块拦截可能未生效 (HTTP $response)"
fi

# 4. 创建浏览器端错误过滤脚本
echo ""
echo "4. 创建浏览器端错误过滤脚本..."

cat > public/vscode-error-filter.js << 'EOF'
// VS Code错误过滤脚本
(function() {
    'use strict';
    
    // 保存原始的console.error方法
    const originalError = console.error;
    
    // 已知的VS Code Web错误模式
    const knownErrorPatterns = [
        /Long running operations during shutdown are unsupported in the web/,
        /No active subscription for controller/,
        /vsda_bg\.wasm.*404/,
        /vsda\.js.*404/,
        /ERR_ABORTED 404.*vsda/,
        /join\.disconnectRemote/,
        /join\.chatSessionStore/,
        /join\.chatEditingSession/
    ];
    
    // 重写console.error方法
    console.error = function(...args) {
        const message = args.join(' ');
        
        // 检查是否是已知的VS Code错误
        const isKnownError = knownErrorPatterns.some(pattern => 
            pattern.test(message)
        );
        
        if (isKnownError) {
            // 静默处理已知错误，只在开发模式下显示
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.log('[已过滤的VS Code错误]:', ...args);
            }
            return;
        }
        
        // 其他错误正常显示
        originalError.apply(console, args);
    };
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        const errorMessage = event.message || event.error?.message || '';
        
        // 过滤已知错误
        const isKnownError = knownErrorPatterns.some(pattern => 
            pattern.test(errorMessage)
        );
        
        if (isKnownError) {
            event.preventDefault();
            return false;
        }
    });
    
    console.log('VS Code错误过滤器已激活');
})();
EOF

echo "✅ 已创建浏览器端错误过滤脚本: public/vscode-error-filter.js"

echo ""
echo "📝 使用说明:"
echo "================================"
echo ""
echo "1. 服务器端修复（已完成）"
echo "   ✅ nginx拦截扩展市场请求"
echo "   ✅ nginx拦截VSDA模块请求"
echo "   ✅ 返回适当的404响应而不是HTML"
echo ""
echo "2. 客户端修复（已完成）"
echo "   ✅ WebIDE组件增强错误处理"
echo "   ✅ 过滤已知的VS Code生命周期错误"
echo "   ✅ 创建浏览器端错误过滤脚本"
echo ""
echo "3. 如何使用错误过滤脚本"
echo "   在HTML中添加以下代码："
echo '   <script src="/vscode-error-filter.js"></script>'
echo ""
echo "4. 预期效果"
echo "   ✅ 控制台不再显示VS Code生命周期错误"
echo "   ✅ 控制台不再显示VSDA模块404错误"
echo "   ✅ 控制台不再显示扩展市场JSON解析错误"
echo "   ✅ 其他真正的错误仍然会正常显示"
echo ""
echo "5. 验证方法"
echo "   - 刷新页面，观察控制台错误是否减少"
echo "   - 在开发模式下仍可看到过滤的错误（标记为[已过滤的VS Code错误]）"
echo "   - 生产环境下完全静默处理"
echo ""
echo "✨ 修复完成！VS Code控制台错误应该大幅减少。"
