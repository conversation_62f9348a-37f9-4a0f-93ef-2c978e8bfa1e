#!/bin/bash

echo "🔧 修复OpenVSCode网络配置"
echo "=================================="

# 检查OpenVSCode是否在运行
if ! curl -s "http://192.168.1.3:3667" > /dev/null; then
    echo "❌ OpenVSCode服务器未运行，请先启动服务器"
    exit 1
fi

echo "✅ OpenVSCode服务器正在运行"

# 检查代理配置问题
echo ""
echo "🔍 检查网络代理配置..."

# 检查是否有代理在127.0.0.1:80运行
if curl -s --connect-timeout 2 "http://127.0.0.1:80" > /dev/null; then
    echo "✅ 本地代理 127.0.0.1:80 正在运行"
else
    echo "❌ 本地代理 127.0.0.1:80 未运行"
    echo ""
    echo "🛠️ 解决方案："
    echo "1. 禁用OpenVSCode的代理配置"
    echo "2. 或者启动本地代理服务"
fi

# 检查能否直接访问open-vsx.org
echo ""
echo "🌐 测试直接访问扩展市场..."
if curl -s --connect-timeout 5 "https://open-vsx.org" > /dev/null; then
    echo "✅ 可以直接访问 open-vsx.org"
    echo ""
    echo "💡 建议: 配置OpenVSCode直接访问扩展市场，无需代理"
    echo ""
    echo "配置方法："
    echo "1. 在OpenVSCode中打开设置 (Ctrl+,)"
    echo "2. 搜索 'proxy'"
    echo "3. 清空所有代理设置"
    echo "4. 或设置 http.proxySupport 为 'off'"
else
    echo "❌ 无法直接访问 open-vsx.org"
    echo "💡 可能需要配置网络代理或防火墙"
fi

echo ""
echo "🔧 快速修复建议："
echo "--------------------------------"
echo "方法1: 在OpenVSCode中执行以下命令"
echo '> Preferences: Open Settings (JSON)'
echo "添加以下配置："
echo '{'
echo '  "http.proxy": "",'
echo '  "http.proxySupport": "off",'
echo '  "extensions.autoUpdate": false'
echo '}'
echo ""
echo "方法2: 忽略扩展更新错误"
echo "- 这些错误不影响IDE核心功能"
echo "- 扩展仍然可以正常使用"
echo ""
echo "方法3: 手动安装扩展"
echo "- 从 open-vsx.org 下载 .vsix 文件"
echo "- 在OpenVSCode中手动安装"

echo ""
echo "✨ 配置完成后重启OpenVSCode服务器即可生效" 