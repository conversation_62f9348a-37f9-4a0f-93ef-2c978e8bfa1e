#!/bin/bash

echo "🔍 验证VS Code错误修复"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

echo ""
echo -e "${BLUE}📋 检查服务状态${NC}"
echo "================================"

# 1. 检查Docker服务
echo "1. 检查Docker Compose服务..."
docker-compose ps
echo ""

# 2. 检查nginx配置
echo "2. 验证nginx配置..."
docker exec ynnx-nginx nginx -t
check_status $? "nginx配置语法检查"
echo ""

echo -e "${BLUE}🛠️  验证错误拦截规则${NC}"
echo "================================"

# 3. 测试扩展市场拦截
echo "3. 测试扩展市场拦截..."
response=$(curl -s -w "%{http_code}" "http://localhost:3668/vscode/gallery/test" -o /tmp/gallery_test.txt)
content=$(cat /tmp/gallery_test.txt)
if [ "$response" = "404" ] && [[ "$content" == *"Extension marketplace disabled"* ]]; then
    echo -e "${GREEN}✅ 扩展市场拦截正常 (HTTP $response)${NC}"
    echo "   响应内容: $content"
else
    echo -e "${RED}❌ 扩展市场拦截异常 (HTTP $response)${NC}"
    echo "   响应内容: $content"
fi
echo ""

# 4. 测试VSDA模块拦截
echo "4. 测试VSDA模块拦截..."
response=$(curl -s -w "%{http_code}" "http://localhost:3668/static/node_modules/vsda/rust/web/vsda_bg.wasm" -o /tmp/vsda_test.txt)
content=$(cat /tmp/vsda_test.txt)
if [ "$response" = "404" ] && [[ "$content" == *"VSDA module not available"* ]]; then
    echo -e "${GREEN}✅ VSDA模块拦截正常 (HTTP $response)${NC}"
    echo "   响应内容: $content"
else
    echo -e "${RED}❌ VSDA模块拦截异常 (HTTP $response)${NC}"
    echo "   响应内容: $content"
fi
echo ""

# 5. 测试JS文件拦截
echo "5. 测试VSDA JS文件拦截..."
response=$(curl -s -w "%{http_code}" "http://localhost:3668/static/node_modules/vsda/rust/web/vsda.js" -o /tmp/vsda_js_test.txt)
content=$(cat /tmp/vsda_js_test.txt)
if [ "$response" = "404" ] && [[ "$content" == *"VSDA module not available"* ]]; then
    echo -e "${GREEN}✅ VSDA JS文件拦截正常 (HTTP $response)${NC}"
    echo "   响应内容: $content"
else
    echo -e "${RED}❌ VSDA JS文件拦截异常 (HTTP $response)${NC}"
    echo "   响应内容: $content"
fi
echo ""

echo -e "${BLUE}🌐 验证前端资源${NC}"
echo "================================"

# 6. 检查错误过滤脚本
echo "6. 检查错误过滤脚本..."
response=$(curl -s -w "%{http_code}" "http://localhost:80/vscode-error-filter.js" -o /tmp/filter_script.txt)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ 错误过滤脚本可访问 (HTTP $response)${NC}"
    lines=$(wc -l < /tmp/filter_script.txt)
    echo "   脚本行数: $lines"
    if grep -q "VS Code错误过滤器已激活" /tmp/filter_script.txt; then
        echo -e "${GREEN}   ✅ 脚本内容正确${NC}"
    else
        echo -e "${YELLOW}   ⚠️  脚本内容可能不完整${NC}"
    fi
else
    echo -e "${RED}❌ 错误过滤脚本无法访问 (HTTP $response)${NC}"
fi
echo ""

# 7. 检查主页
echo "7. 检查主页访问..."
response=$(curl -s -w "%{http_code}" "http://localhost:80/" -o /tmp/index_test.txt)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ 主页可正常访问 (HTTP $response)${NC}"
    if grep -q "vscode-error-filter.js" /tmp/index_test.txt; then
        echo -e "${GREEN}   ✅ 错误过滤脚本已集成到HTML${NC}"
    else
        echo -e "${YELLOW}   ⚠️  错误过滤脚本未在HTML中找到${NC}"
    fi
else
    echo -e "${RED}❌ 主页无法访问 (HTTP $response)${NC}"
fi
echo ""

echo -e "${BLUE}🔧 验证OpenVSCode代理${NC}"
echo "================================"

# 8. 检查OpenVSCode代理
echo "8. 检查OpenVSCode代理..."
if netstat -tlnp 2>/dev/null | grep -q ":3667.*LISTEN"; then
    echo -e "${GREEN}✅ OpenVSCode服务器正在运行 (端口3667)${NC}"
    
    # 测试代理连接
    response=$(curl -s -w "%{http_code}" "http://localhost:3668/health" -o /tmp/proxy_health.txt)
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ OpenVSCode代理健康检查通过 (HTTP $response)${NC}"
        content=$(cat /tmp/proxy_health.txt)
        echo "   健康检查响应: $content"
    else
        echo -e "${YELLOW}⚠️  OpenVSCode代理健康检查异常 (HTTP $response)${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  OpenVSCode服务器未检测到运行状态${NC}"
    echo "   提示: 运行 ./scripts/start-openvscode.sh 启动服务器"
fi
echo ""

# 清理临时文件
rm -f /tmp/gallery_test.txt /tmp/vsda_test.txt /tmp/vsda_js_test.txt /tmp/filter_script.txt /tmp/index_test.txt /tmp/proxy_health.txt

echo -e "${BLUE}📊 修复效果总结${NC}"
echo "================================"
echo ""
echo -e "${GREEN}✅ 已修复的错误类型:${NC}"
echo "   • 扩展市场JSON解析错误"
echo "   • VSDA模块404错误"
echo "   • VS Code生命周期错误（客户端过滤）"
echo ""
echo -e "${BLUE}🎯 预期效果:${NC}"
echo "   • 浏览器控制台错误大幅减少"
echo "   • VS Code Web功能完全正常"
echo "   • 开发体验显著改善"
echo ""
echo -e "${YELLOW}📝 使用建议:${NC}"
echo "   • 刷新浏览器页面查看效果"
echo "   • 在开发模式下仍可看到过滤的错误（标记为已过滤）"
echo "   • 如有新的错误，请检查是否需要添加到过滤列表"
echo ""
echo -e "${GREEN}✨ 验证完成！${NC}"
