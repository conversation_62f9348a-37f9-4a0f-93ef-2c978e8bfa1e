#!/bin/bash

echo "🧪 测试iframe-resizer集成"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo ""
echo -e "${BLUE}📋 检查服务状态${NC}"
echo "================================"

# 1. 检查Docker容器状态
echo "1. 检查Docker容器状态..."
docker_status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(ynnx-|openvscode)")
if [ -n "$docker_status" ]; then
    echo -e "${GREEN}✅ Docker容器运行状态:${NC}"
    echo "$docker_status"
else
    echo -e "${RED}❌ 未找到运行中的Docker容器${NC}"
fi

echo ""

# 2. 检查端口占用
echo "2. 检查端口占用..."
ports=(80 3667 3668)
for port in "${ports[@]}"; do
    if netstat -tlnp 2>/dev/null | grep -q ":$port.*LISTEN"; then
        echo -e "${GREEN}✅ 端口 $port 正在监听${NC}"
    else
        echo -e "${RED}❌ 端口 $port 未监听${NC}"
    fi
done

echo ""
echo -e "${BLUE}🌐 测试网络连接${NC}"
echo "================================"

# 3. 测试OpenVSCode直接连接
echo "3. 测试OpenVSCode直接连接..."
response=$(curl -s -w "%{http_code}" "http://***********:3667?tkn=tk-ynnx-llm" -o /tmp/openvscode_direct.html)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ OpenVSCode直接连接正常 (HTTP $response)${NC}"
    if grep -q "Visual Studio Code" /tmp/openvscode_direct.html; then
        echo -e "${GREEN}   ✅ 页面内容包含VS Code标识${NC}"
    else
        echo -e "${YELLOW}   ⚠️  页面内容可能不完整${NC}"
    fi
else
    echo -e "${RED}❌ OpenVSCode直接连接失败 (HTTP $response)${NC}"
fi

# 4. 测试nginx代理连接
echo "4. 测试nginx代理连接..."
response=$(curl -s -w "%{http_code}" "http://localhost:3668?tkn=tk-ynnx-llm" -o /tmp/openvscode_proxy.html)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ nginx代理连接正常 (HTTP $response)${NC}"
    if grep -q "Visual Studio Code" /tmp/openvscode_proxy.html; then
        echo -e "${GREEN}   ✅ 代理页面内容包含VS Code标识${NC}"
    else
        echo -e "${YELLOW}   ⚠️  代理页面内容可能不完整${NC}"
    fi
else
    echo -e "${RED}❌ nginx代理连接失败 (HTTP $response)${NC}"
fi

# 5. 测试主应用
echo "5. 测试主应用..."
response=$(curl -s -w "%{http_code}" "http://localhost:80" -o /tmp/main_app.html)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ 主应用连接正常 (HTTP $response)${NC}"
    if grep -q "iframe-resizer-react" /tmp/main_app.html; then
        echo -e "${GREEN}   ✅ 页面包含iframe-resizer依赖${NC}"
    else
        echo -e "${YELLOW}   ⚠️  页面可能未包含iframe-resizer依赖${NC}"
    fi
    if grep -q "advanced-vscode-error-filter.js" /tmp/main_app.html; then
        echo -e "${GREEN}   ✅ 页面包含错误过滤脚本${NC}"
    else
        echo -e "${YELLOW}   ⚠️  页面可能未包含错误过滤脚本${NC}"
    fi
else
    echo -e "${RED}❌ 主应用连接失败 (HTTP $response)${NC}"
fi

echo ""
echo -e "${BLUE}🔧 检查iframe支持配置${NC}"
echo "================================"

# 6. 检查iframe相关头部
echo "6. 检查iframe相关头部..."
headers=$(curl -s -I "http://localhost:3668?tkn=tk-ynnx-llm" | grep -i "x-frame-options\|content-security-policy")
if [ -n "$headers" ]; then
    echo -e "${GREEN}✅ iframe支持头部已配置:${NC}"
    echo "$headers" | sed 's/^/   /'
else
    echo -e "${YELLOW}⚠️  iframe支持头部未找到${NC}"
fi

# 7. 检查CORS配置
echo "7. 检查CORS配置..."
cors_headers=$(curl -s -I "http://localhost:3668?tkn=tk-ynnx-llm" | grep -i "access-control")
if [ -n "$cors_headers" ]; then
    echo -e "${GREEN}✅ CORS头部已配置:${NC}"
    echo "$cors_headers" | sed 's/^/   /'
else
    echo -e "${YELLOW}⚠️  CORS头部未找到${NC}"
fi

echo ""
echo -e "${BLUE}📦 检查前端依赖${NC}"
echo "================================"

# 8. 检查npm依赖
echo "8. 检查npm依赖..."
if npm list iframe-resizer-react >/dev/null 2>&1; then
    version=$(npm list iframe-resizer-react --depth=0 2>/dev/null | grep iframe-resizer-react | awk '{print $2}')
    echo -e "${GREEN}✅ iframe-resizer-react已安装 (版本: $version)${NC}"
else
    echo -e "${RED}❌ iframe-resizer-react未安装${NC}"
fi

if npm list iframe-resizer >/dev/null 2>&1; then
    version=$(npm list iframe-resizer --depth=0 2>/dev/null | grep iframe-resizer | awk '{print $2}')
    echo -e "${GREEN}✅ iframe-resizer已安装 (版本: $version)${NC}"
else
    echo -e "${RED}❌ iframe-resizer未安装${NC}"
fi

# 9. 检查构建文件
echo "9. 检查构建文件..."
if [ -f "dist/index.html" ]; then
    echo -e "${GREEN}✅ 构建文件存在${NC}"
    if grep -q "iframe-resizer" dist/assets/*.js 2>/dev/null; then
        echo -e "${GREEN}   ✅ 构建文件包含iframe-resizer代码${NC}"
    else
        echo -e "${YELLOW}   ⚠️  构建文件可能未包含iframe-resizer代码${NC}"
    fi
else
    echo -e "${RED}❌ 构建文件不存在，请运行 npm run build${NC}"
fi

# 清理临时文件
rm -f /tmp/openvscode_direct.html /tmp/openvscode_proxy.html /tmp/main_app.html

echo ""
echo -e "${BLUE}📊 测试结果总结${NC}"
echo "================================"

# 计算成功率
total_tests=9
passed_tests=0

# 重新检查关键测试
if docker ps --format "{{.Names}}" | grep -q "ynnx-openvscode"; then
    ((passed_tests++))
fi

if netstat -tlnp 2>/dev/null | grep -q ":3667.*LISTEN"; then
    ((passed_tests++))
fi

if curl -s -w "%{http_code}" "http://***********:3667?tkn=tk-ynnx-llm" -o /dev/null | grep -q "200"; then
    ((passed_tests++))
fi

if curl -s -w "%{http_code}" "http://localhost:3668?tkn=tk-ynnx-llm" -o /dev/null | grep -q "200"; then
    ((passed_tests++))
fi

if curl -s -w "%{http_code}" "http://localhost:80" -o /dev/null | grep -q "200"; then
    ((passed_tests++))
fi

if npm list iframe-resizer-react >/dev/null 2>&1; then
    ((passed_tests++))
fi

if npm list iframe-resizer >/dev/null 2>&1; then
    ((passed_tests++))
fi

if [ -f "dist/index.html" ]; then
    ((passed_tests++))
fi

if [ -f "src/components/WebIDESection.jsx" ] && grep -q "IframeResizer" src/components/WebIDESection.jsx; then
    ((passed_tests++))
fi

success_rate=$((passed_tests * 100 / total_tests))

echo ""
echo -e "${BLUE}测试通过率: $passed_tests/$total_tests ($success_rate%)${NC}"

if [ $success_rate -ge 80 ]; then
    echo -e "${GREEN}🎉 iframe-resizer集成测试基本通过！${NC}"
    echo ""
    echo -e "${GREEN}✅ 主要功能已就绪:${NC}"
    echo "   • OpenVSCode服务器运行正常"
    echo "   • nginx代理配置正确"
    echo "   • iframe-resizer依赖已安装"
    echo "   • WebIDE组件已升级"
    echo ""
    echo -e "${BLUE}🚀 下一步:${NC}"
    echo "   1. 在浏览器中访问: http://localhost:80"
    echo "   2. 登录并进入WebIDE部分"
    echo "   3. 测试VS Code功能和插件页面"
    echo "   4. 观察控制台错误是否减少"
elif [ $success_rate -ge 60 ]; then
    echo -e "${YELLOW}⚠️  iframe-resizer集成部分完成，需要一些调整${NC}"
    echo ""
    echo -e "${YELLOW}需要检查的项目:${NC}"
    echo "   • 确保所有Docker容器正常运行"
    echo "   • 检查nginx配置是否正确"
    echo "   • 验证前端构建是否包含新依赖"
else
    echo -e "${RED}❌ iframe-resizer集成存在问题，需要修复${NC}"
    echo ""
    echo -e "${RED}需要修复的项目:${NC}"
    echo "   • 检查Docker服务状态"
    echo "   • 重新安装npm依赖"
    echo "   • 重新构建前端应用"
    echo "   • 检查nginx配置"
fi

echo ""
echo -e "${BLUE}📝 故障排除建议:${NC}"
echo "================================"
echo ""
echo "如果遇到问题，请按以下顺序检查："
echo ""
echo "1. 重启OpenVSCode服务器:"
echo "   docker restart ynnx-openvscode"
echo ""
echo "2. 重新构建前端应用:"
echo "   npm run build"
echo ""
echo "3. 重启Docker容器:"
echo "   docker-compose down && docker-compose up -d"
echo ""
echo "4. 检查浏览器控制台错误"
echo ""
echo "5. 如果插件页面仍有问题，可能需要进一步调整iframe配置"
echo ""
echo -e "${GREEN}✨ 测试完成！${NC}"
