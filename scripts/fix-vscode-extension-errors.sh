#!/bin/bash

echo "🔧 修复VS Code扩展市场错误"
echo "================================"

echo ""
echo "📋 问题分析:"
echo "  - VS Code Web版本试图访问 open-vsx.org 扩展市场"
echo "  - 请求被nginx代理拦截，返回HTML而不是JSON"
echo "  - 这会导致浏览器控制台出现JSON解析错误"

echo ""
echo "🛠️  解决方案:"
echo "  1. 已在nginx代理中添加扩展市场请求拦截"
echo "  2. 返回404状态码和JSON错误信息"
echo "  3. 这样可以避免HTML解析错误"

echo ""
echo "🔍 检查nginx配置..."

# 检查nginx配置是否包含扩展市场拦截
if grep -q "vscode/gallery" nginx/conf.d/webide-proxy.conf; then
    echo "✅ nginx配置已包含扩展市场拦截规则"
else
    echo "❌ nginx配置缺少扩展市场拦截规则"
    echo "正在添加..."
    
    # 备份原配置
    cp nginx/conf.d/webide-proxy.conf nginx/conf.d/webide-proxy.conf.backup
    
    # 在location /之前添加扩展市场拦截
    sed -i '/# 主要代理配置/i\    # 拦截扩展市场请求，返回空响应避免错误\
    location ~* /vscode/gallery/ {\
        access_log off;\
        return 404 '\''{"error": "Extension marketplace disabled"}'\'';\
        add_header Content-Type application/json;\
    }\
' nginx/conf.d/webide-proxy.conf
    
    echo "✅ 已添加扩展市场拦截规则"
fi

echo ""
echo "🔄 重启nginx应用配置..."
docker-compose restart nginx

if [ $? -eq 0 ]; then
    echo "✅ nginx重启成功"
else
    echo "❌ nginx重启失败"
    exit 1
fi

echo ""
echo "🧪 测试扩展市场拦截..."
response=$(curl -s -w "%{http_code}" "http://localhost:3668/vscode/gallery/test" -o /dev/null)
if [ "$response" = "404" ]; then
    echo "✅ 扩展市场拦截工作正常 (HTTP 404)"
else
    echo "⚠️  扩展市场拦截可能未生效 (HTTP $response)"
fi

echo ""
echo "📝 使用说明:"
echo "================================"
echo ""
echo "1. 浏览器控制台错误已被修复"
echo "   - 扩展市场请求现在返回404而不是HTML"
echo "   - 不再出现JSON解析错误"
echo ""
echo "2. VS Code Web功能不受影响"
echo "   - 核心编辑功能正常"
echo "   - 文件管理正常"
echo "   - 终端功能正常"
echo ""
echo "3. 扩展管理"
echo "   - 自动扩展更新已禁用"
echo "   - 可以手动安装本地扩展"
echo "   - 内置扩展仍然可用"
echo ""
echo "4. 访问地址"
echo "   - 代理访问: http://localhost:3668?tkn=tk-ynnx-llm"
echo "   - 直接访问: http://***********:3667?tkn=tk-ynnx-llm"
echo ""
echo "✨ 修复完成！浏览器控制台错误应该已经消失。"
