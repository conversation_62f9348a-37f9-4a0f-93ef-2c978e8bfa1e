#!/bin/bash
# OpenVSCode服务器测试脚本
# 用于验证服务器配置是否正确

# 配置变量
OPENVSCODE_URL="http://***********:3667"
TOKEN="tk-ynnx-llm"
TEST_URL="$OPENVSCODE_URL?tkn=$TOKEN"

echo "🧪 测试OpenVSCode服务器配置..."
echo "服务器URL: $OPENVSCODE_URL"
echo "Token: $TOKEN"
echo "完整URL: $TEST_URL"
echo ""

# 测试1: 检查服务器是否运行
echo "1. 检查服务器是否运行..."
if curl -s --max-time 5 "$OPENVSCODE_URL" > /dev/null; then
    echo "✅ 服务器正在运行"
else
    echo "❌ 服务器未运行或无法连接"
    echo "请先启动OpenVSCode服务器："
    echo "  ./scripts/start-openvscode-server.sh"
    exit 1
fi

# 测试2: 检查Token认证
echo ""
echo "2. 测试Token认证..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL")

case $HTTP_STATUS in
    200)
        echo "✅ Token认证成功 (HTTP $HTTP_STATUS)"
        echo "Web IDE应该可以正常工作"
        ;;
    403)
        echo "❌ Token认证失败 (HTTP $HTTP_STATUS)"
        echo "可能的原因："
        echo "  - OpenVSCode服务器未正确配置CONNECTION_TOKEN"
        echo "  - Token值不匹配"
        echo "  - Token参数名称错误"
        echo ""
        echo "建议解决方案："
        echo "  1. 停止当前服务器"
        echo "  2. 运行: ./scripts/start-openvscode-server.sh"
        echo "  3. 重新测试"
        ;;
    404)
        echo "❌ 服务器路径错误 (HTTP $HTTP_STATUS)"
        echo "请检查服务器URL是否正确"
        ;;
    *)
        echo "❌ 未知错误 (HTTP $HTTP_STATUS)"
        echo "请检查服务器状态和配置"
        ;;
esac

# 测试3: 检查CORS设置
echo ""
echo "3. 检查CORS设置..."
CORS_HEADERS=$(curl -s -I -H "Origin: http://localhost:5174" "$TEST_URL" | grep -i "access-control\|x-frame-options")

if [ -z "$CORS_HEADERS" ]; then
    echo "⚠️  未检测到CORS头，可能需要配置Nginx代理"
else
    echo "✅ 检测到CORS相关头："
    echo "$CORS_HEADERS"
fi

# 测试4: 端口连通性
echo ""
echo "4. 测试端口连通性..."
if nc -z *********** 3667 2>/dev/null; then
    echo "✅ 端口 3667 可访问"
else
    echo "❌ 端口 3667 无法访问"
    echo "请检查："
    echo "  - 防火墙设置"
    echo "  - 服务器是否在正确端口运行"
    echo "  - 网络连接"
fi

echo ""
echo "📋 测试完成！"
echo "如果所有测试都通过，Web IDE应该可以正常工作。"
echo "如果仍有问题，请检查浏览器开发者工具的控制台。" 