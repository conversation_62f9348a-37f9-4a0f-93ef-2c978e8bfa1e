// 高级VS Code错误过滤脚本
(function() {
    'use strict';
    
    console.log('🛡️ 启动高级VS Code错误过滤器...');
    
    // 保存原始方法
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalLog = console.log;
    
    // 扩展的错误模式列表
    const errorPatterns = [
        // 生命周期错误
        /Long running operations during shutdown are unsupported in the web/,
        /No active subscription for controller/,
        /join\.(disconnectRemote|chatSessionStore|chatEditingSession)/,
        
        // VSDA模块错误
        /vsda_bg\.wasm.*404/,
        /vsda\.js.*404/,
        /ERR_ABORTED 404.*vsda/,
        /insider-.*\/static\/node_modules\/vsda/,
        /node_modules\/vsda\/rust\/web/,
        
        // 权限策略错误
        /Potential permissions policy violation: (usb|serial|hid) is not allowed/,
        /permissions policy violation/i,
        
        // 扩展配置错误
        /Cannot register.*This property is already registered/,
        /redhat\.vscode-yaml.*Cannot register/,
        /redhat\.telemetry\.enabled.*already registered/,
        
        // Clangd配置错误
        /Ignoring.*as.*is "prompt"/,
        /clangd\.onConfigChanged/,
        /Ignoring clangd\.onConfigChanged\.forceEnable/,
        
        // 扩展市场错误
        /open-vsx\.org.*gallery/,
        /Extension marketplace/,
        /Unexpected token.*<!doctype/,
        
        // 其他常见VS Code Web错误
        /Failed to load resource.*vsda/,
        /net::ERR_ABORTED.*vsda/,
        /404.*vsda/
    ];
    
    // 警告模式列表
    const warnPatterns = [
        /redhat\.vscode-yaml.*Cannot register/,
        /redhat\.telemetry\.enabled/,
        /This property is already registered/
    ];
    
    // 检查是否为已知错误
    function isKnownError(message, patterns) {
        return patterns.some(pattern => pattern.test(message));
    }
    
    // 格式化消息
    function formatMessage(args) {
        return args.map(arg => {
            if (typeof arg === 'object') {
                try {
                    return JSON.stringify(arg);
                } catch (e) {
                    return String(arg);
                }
            }
            return String(arg);
        }).join(' ');
    }
    
    // 重写console.error
    console.error = function(...args) {
        const message = formatMessage(args);
        
        if (isKnownError(message, errorPatterns)) {
            // 在开发环境下显示过滤信息
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                originalLog('%c[已过滤的VS Code错误]', 'color: #888; font-style: italic;', ...args);
            }
            return;
        }
        
        // 其他错误正常显示
        originalError.apply(console, args);
    };
    
    // 重写console.warn
    console.warn = function(...args) {
        const message = formatMessage(args);
        
        if (isKnownError(message, warnPatterns)) {
            // 在开发环境下显示过滤信息
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                originalLog('%c[已过滤的VS Code警告]', 'color: #888; font-style: italic;', ...args);
            }
            return;
        }
        
        // 其他警告正常显示
        originalWarn.apply(console, args);
    };
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        const errorMessage = event.message || event.error?.message || '';
        
        if (isKnownError(errorMessage, errorPatterns)) {
            event.preventDefault();
            return false;
        }
    });
    
    // 全局未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
        const reason = event.reason;
        const message = reason?.message || String(reason);
        
        if (isKnownError(message, errorPatterns)) {
            event.preventDefault();
            return false;
        }
    });
    
    // 网络请求拦截（如果可能）
    if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            
            // 检查是否为VSDA相关请求
            if (typeof url === 'string' && url.includes('vsda')) {
                console.log('%c[已拦截的VSDA请求]', 'color: #888; font-style: italic;', url);
                return Promise.reject(new Error('VSDA module not available in this environment'));
            }
            
            return originalFetch.apply(this, args);
        };
    }
    
    console.log('✅ 高级VS Code错误过滤器已激活');
    console.log('📊 过滤规则数量:', errorPatterns.length + warnPatterns.length);
    
})();
