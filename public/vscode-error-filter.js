// VS Code错误过滤脚本
(function() {
    'use strict';
    
    // 保存原始的console.error方法
    const originalError = console.error;
    
    // 已知的VS Code Web错误模式
    const knownErrorPatterns = [
        /Long running operations during shutdown are unsupported in the web/,
        /No active subscription for controller/,
        /vsda_bg\.wasm.*404/,
        /vsda\.js.*404/,
        /ERR_ABORTED 404.*vsda/,
        /join\.disconnectRemote/,
        /join\.chatSessionStore/,
        /join\.chatEditingSession/,
        /insider-.*\/static\/node_modules\/vsda/,
        /Potential permissions policy violation: (usb|serial|hid) is not allowed/,
        /Cannot register.*This property is already registered/,
        /Ignoring.*as.*is "prompt"/,
        /redhat\.vscode-yaml.*Cannot register/,
        /clangd\.onConfigChanged/
    ];
    
    // 重写console.error方法
    console.error = function(...args) {
        const message = args.join(' ');
        
        // 检查是否是已知的VS Code错误
        const isKnownError = knownErrorPatterns.some(pattern => 
            pattern.test(message)
        );
        
        if (isKnownError) {
            // 静默处理已知错误，只在开发模式下显示
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.log('[已过滤的VS Code错误]:', ...args);
            }
            return;
        }
        
        // 其他错误正常显示
        originalError.apply(console, args);
    };
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        const errorMessage = event.message || event.error?.message || '';
        
        // 过滤已知错误
        const isKnownError = knownErrorPatterns.some(pattern => 
            pattern.test(errorMessage)
        );
        
        if (isKnownError) {
            event.preventDefault();
            return false;
        }
    });
    
    console.log('VS Code错误过滤器已激活');
})();
