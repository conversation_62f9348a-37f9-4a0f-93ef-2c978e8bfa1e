# Web IDE 使用指南

> 基于 OpenVSCode 的云端开发环境，提供完整的代码编辑、调试和项目管理功能

## 🌟 功能概述

Web IDE 是本平台集成的云端开发环境，基于 OpenVSCode 技术构建，为开发者提供：

- **完整的代码编辑器** - 支持语法高亮、智能补全、错误检查
- **多语言支持** - 内置对主流编程语言的支持
- **扩展生态** - 兼容 VS Code 扩展，可自定义开发环境
- **项目管理** - 完整的文件系统操作和项目结构管理
- **调试功能** - 内置调试器，支持断点、变量监视等
- **版本控制** - 集成 Git 支持，方便代码版本管理

## 🚀 快速开始

### 前置条件

1. **登录账户** - Web IDE 需要用户登录后才能访问
2. **服务器运行** - 确保 OpenVSCode 服务器正常运行
3. **网络连接** - 稳定的网络连接确保最佳使用体验

### 访问方式

1. **平台内访问**
   - 登录开发平台
   - 点击导航栏中的 "Web IDE" 按钮
   - 等待 IDE 界面加载完成

2. **独立窗口访问**
   - 在 Web IDE 界面点击 "新窗口打开" 按钮
   - 获得更大的开发空间
   - 避免浏览器标签页切换干扰

## 🎮 界面操作

### 控制按钮说明

- **🔄 刷新** - 重新加载 IDE 界面，解决加载问题
- **⛶ 全屏** - 切换全屏模式，专注代码开发
- **🔗 新窗口打开** - 在新标签页中打开 IDE
- **🧪 测试URL** - 验证 IDE 服务器连接状态

### 界面布局

- **文件浏览器** - 左侧面板显示项目文件结构
- **编辑器区域** - 中央主要工作区域，支持多标签页
- **终端面板** - 底部集成终端，支持命令行操作
- **扩展面板** - 管理和安装 VS Code 扩展

## 🛠️ 核心功能

### 文件管理
- **创建文件** - 右键菜单或快捷键创建新文件
- **文件夹操作** - 支持创建、删除、重命名文件夹
- **文件搜索** - 全局文件搜索和内容搜索
- **文件上传** - 拖拽或选择文件上传到项目

### 代码编辑
- **语法高亮** - 自动识别编程语言并提供语法着色
- **智能补全** - IntelliSense 代码补全和提示
- **错误检查** - 实时语法错误和问题检测
- **代码格式化** - 自动格式化代码，保持代码规范

### 调试功能
- **断点调试** - 设置断点，逐步执行代码
- **变量监视** - 查看变量值和对象状态
- **调用堆栈** - 跟踪函数调用路径
- **控制台输出** - 查看程序输出和调试信息

### 版本控制
- **Git 集成** - 内置 Git 支持，可视化版本控制
- **变更跟踪** - 实时显示文件修改状态
- **提交历史** - 查看和管理提交记录
- **分支管理** - 创建、切换和合并分支

## ⚙️ 配置管理

### 环境变量配置

在项目的 `.env` 文件中配置 Web IDE 相关参数：

```env
# Web IDE 服务器配置
VITE_WEBIDE_BASE_URL=http://192.168.1.3:3667
VITE_WEBIDE_TOKEN=tk-ynnx-llm
VITE_WEBIDE_TOKEN_PARAM=tkn
```

### 自定义设置

- **主题设置** - 切换编辑器主题（深色/浅色）
- **字体配置** - 调整编辑器字体大小和样式
- **快捷键** - 自定义快捷键映射
- **编辑器行为** - 配置缩进、换行等编辑器行为

### 扩展管理

1. **安装扩展**
   - 点击左侧扩展图标
   - 搜索所需扩展
   - 点击安装按钮

2. **推荐扩展**
   - **Chinese Language Pack** - 中文语言包
   - **Prettier** - 代码格式化工具
   - **GitLens** - 增强的 Git 功能
   - **Live Share** - 实时协作编程

## 🔧 故障排除

### 常见问题解决

**1. IDE 无法加载**
- 检查服务器状态是否正常
- 尝试刷新页面或清除浏览器缓存
- 使用"测试URL"按钮验证连接

**2. 功能异常**
- 检查网络连接稳定性
- 尝试在新窗口中打开 IDE
- 查看浏览器控制台错误信息

**3. 性能问题**
- 关闭不必要的编辑器标签页
- 减少同时打开的大文件数量
- 清理项目中的临时文件

### 调试信息

IDE 界面会显示详细的配置信息：
- 服务器地址和连接状态
- 认证参数配置
- iframe 加载状态
- 实时连接状态

## 💡 使用技巧

### 提高效率

1. **快捷键使用**
   - `Ctrl+P` - 快速打开文件
   - `Ctrl+Shift+P` - 命令面板
   - `Ctrl+` ` - 打开/关闭终端
   - `F5` - 开始调试

2. **多光标编辑**
   - `Alt+Click` - 添加多个光标
   - `Ctrl+Alt+↑/↓` - 在上/下行添加光标
   - `Ctrl+D` - 选择相同内容

3. **分屏编辑**
   - `Ctrl+\` - 分割编辑器
   - `Ctrl+1/2/3` - 聚焦到不同编辑器组

### 最佳实践

1. **项目组织**
   - 合理规划文件夹结构
   - 使用有意义的文件命名
   - 定期整理和删除无用文件

2. **代码管理**
   - 及时提交代码变更
   - 编写清晰的提交信息
   - 定期备份重要代码

3. **性能优化**
   - 避免打开过大的文件
   - 合理使用扩展功能
   - 定期清理编辑器缓存

## 🔐 安全注意事项

- **账户安全** - 定期更改登录密码
- **代码保护** - 避免在代码中硬编码敏感信息
- **网络安全** - 确保在安全的网络环境中使用
- **访问控制** - 注意文件和项目的访问权限设置

## 📋 常见问答

**Q: Web IDE 支持哪些编程语言？**
A: 支持主流编程语言，包括 JavaScript、Python、Java、Go、C++、HTML/CSS 等，可通过扩展支持更多语言。

**Q: 可以在 Web IDE 中运行代码吗？**
A: 是的，Web IDE 集成了终端功能，可以直接运行代码和命令。

**Q: 如何保存和备份代码？**
A: 代码会自动保存到服务器，建议使用 Git 进行版本控制和备份。

**Q: Web IDE 有文件大小限制吗？**
A: 建议单个文件不超过 10MB，大型文件可能影响编辑器性能。

**Q: 支持团队协作吗？**
A: 可以通过 Git 进行代码协作，或安装 Live Share 扩展进行实时协作。

## 📞 技术支持

如果遇到问题无法解决：

1. **查看控制台** - 浏览器开发者工具中的错误信息
2. **检查网络** - 确认网络连接和服务器状态
3. **联系支持** - 向技术支持团队反馈问题
4. **查看日志** - 查看服务器端的相关日志信息

---

*Web IDE 为您提供专业的云端开发体验，助力高效代码开发！*