{"categories": [{"id": "all", "name": "全部文档", "icon": "FaBook"}, {"id": "cline", "name": "Cline 插件", "icon": "FaCode"}, {"id": "roocode", "name": "RooCode 插件", "icon": "FaCode"}, {"id": "jetbrains", "name": "JetBrains AI Assistant", "icon": "FaLaptopCode"}, {"id": "faq", "name": "常见问题", "icon": "FaQuestionCircle"}, {"id": "webide", "name": "Web IDE", "icon": "FaCode"}], "documents": {"cline": [{"id": "cline-install-guide", "title": "Cline插件安装配置指南", "description": "详细的Cline AI编程助手安装和配置教程，包含API配置和使用技巧", "readTime": "8 分钟", "tags": ["安装", "配置", "API", "VS Code"], "file": "cline/install-guide.md"}, {"id": "cline-advanced-features", "title": "Cline高级功能与最佳实践", "description": "Cline的高级功能、智能对话技巧、工具集成和故障排除指南", "readTime": "12 分钟", "tags": ["高级功能", "最佳实践", "工具集成", "故障排除"], "file": "cline/advanced-features.md"}], "roocode": [{"id": "roocode-install-guide", "title": "RooCode插件安装配置指南", "description": "RooCode多模式AI助手的详细安装配置教程，支持VS Code、Cursor等编辑器", "readTime": "8 分钟", "tags": ["安装", "配置", "VS Code", "<PERSON><PERSON><PERSON>"], "file": "roocode/install-guide.md"}, {"id": "roocode-multi-mode", "title": "RooCode多模式使用指南", "description": "RooCode四种工作模式详解：Code、Architect、Ask、Debug模式的特点和使用场景", "readTime": "12 分钟", "tags": ["多模式", "Code模式", "Architect模式", "Ask模式", "Debug模式"], "file": "roocode/multi-mode.md"}], "jetbrains": [{"id": "jetbrains-install-guide", "title": "JetBrains AI Assistant安装指南", "description": "JetBrains系列IDE的AI助手插件详细安装配置教程，支持IntelliJ、PyCharm等", "readTime": "8 分钟", "tags": ["JetBrains", "IntelliJ", "PyCharm", "AI助手", "安装"], "file": "jetbrains/install-guide.md"}, {"id": "jetbrains-features", "title": "JetBrains AI Assistant功能详解", "description": "JetBrains AI Assistant的核心功能、使用技巧和高级特性详细介绍", "readTime": "10 分钟", "tags": ["功能详解", "代码生成", "智能补全", "重构", "测试"], "file": "jetbrains/features.md"}], "faq": [{"id": "common-faq", "title": "常见问题解答", "description": "使用AI开发工具过程中的常见问题和详细解决方案", "readTime": "6 分钟", "tags": ["FAQ", "故障排除", "配置问题"], "file": "faq/common-faq.md"}], "webide": [{"id": "webide-usage-guide", "title": "Web IDE 使用指南", "description": "基于OpenVSCode的云端开发环境使用指南，包含界面操作、功能介绍和故障排除", "readTime": "15 分钟", "tags": ["Web IDE", "OpenVSCode", "云端开发", "使用指南", "故障排除"], "file": "webide/usage-guide.md"}]}}