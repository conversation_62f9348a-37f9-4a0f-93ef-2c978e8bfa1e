{"permissions": {"allow": ["WebFetch(domain:docs.copilotkit.ai)", "WebFetch(domain:docs.copilotkit.ai)", "WebFetch(domain:docs.copilotkit.ai)", "Bash(rg:*)", "Bash(npm run lint)", "Bash(rm:*)", "Bash(npm run build:*)", "Bash(timeout 10s npm run dev)", "<PERSON><PERSON>(true)", "Bash(ls:*)", "Bash(npm install:*)", "Bash(sudo chown:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm ls:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(sed:*)", "Bash(grep:*)"], "deny": []}}