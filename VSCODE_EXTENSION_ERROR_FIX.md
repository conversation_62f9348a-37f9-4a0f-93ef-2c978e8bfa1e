# VS Code扩展市场错误修复指南

## 🎯 问题描述

在使用docker-compose启动项目后，浏览器控制台出现以下错误：

```
workbench.js:35   ERR Error while getting the latest version for the extension ms-vscode.cmake-tools from https://open-vsx.org/vscode/gallery/vscode/{publisher}/{name}/latest. Trying the fallback https://open-vsx.org/vscode/gallery/{publisher}/{name}/latest Unexpected token '<', "<!doctype "... is not valid JSON
```

## 🔍 问题分析

### 根本原因
1. **VS Code Web版本**试图访问外部扩展市场API (`https://open-vsx.org`)
2. **nginx代理拦截**了这些请求，返回了应用的HTML页面而不是预期的JSON
3. **JSON解析失败**导致浏览器控制台出现错误

### 技术细节
- VS Code Web会自动尝试获取扩展更新信息
- 请求路径: `/vscode/gallery/vscode/{publisher}/{name}/latest`
- nginx的默认路由 `location /` 捕获了这些请求
- 返回了`index.html`而不是JSON响应

## ✅ 解决方案

### 1. nginx配置修复

已在 `nginx/conf.d/webide-proxy.conf` 中添加扩展市场请求拦截：

```nginx
# 拦截扩展市场请求，返回空响应避免错误
location ~* /vscode/gallery/ {
    access_log off;
    return 404 '{"error": "Extension marketplace disabled"}';
    add_header Content-Type application/json;
}
```

### 2. 自动修复脚本

运行修复脚本：
```bash
./scripts/fix-vscode-extension-errors.sh
```

### 3. 手动验证

测试扩展市场拦截：
```bash
curl -s "http://localhost:3668/vscode/gallery/test"
# 应该返回: {"error": "Extension marketplace disabled"}
```

## 🚀 部署步骤

### 完整部署流程

1. **启动OpenVSCode服务器**（如果未运行）：
   ```bash
   ./scripts/start-openvscode.sh
   ```

2. **重启nginx应用配置**：
   ```bash
   docker-compose restart nginx
   ```

3. **验证修复**：
   ```bash
   ./scripts/fix-vscode-extension-errors.sh
   ```

### 访问地址

- **代理访问**（推荐）: http://localhost:3668?tkn=tk-ynnx-llm
- **直接访问**: http://***********:3667?tkn=tk-ynnx-llm

## 📋 功能影响

### ✅ 正常功能
- VS Code核心编辑功能
- 文件管理和浏览
- 终端和命令行
- 内置扩展
- WebSocket连接
- 语法高亮和智能提示

### ⚠️ 受限功能
- 自动扩展更新（已禁用）
- 在线扩展市场浏览
- 扩展自动安装

### 🔧 替代方案
- **手动安装扩展**: 下载`.vsix`文件手动安装
- **本地扩展**: 使用预装扩展
- **离线模式**: 适合内网环境

## 🛠️ 故障排除

### 常见问题

#### 1. 仍然看到控制台错误
**解决方案**:
```bash
# 清除浏览器缓存
# 重启nginx
docker-compose restart nginx
# 验证配置
curl -s "http://localhost:3668/vscode/gallery/test"
```

#### 2. OpenVSCode无法访问
**解决方案**:
```bash
# 检查服务状态
netstat -tlnp | grep 3667
# 重启OpenVSCode
./scripts/start-openvscode.sh
```

#### 3. 代理连接失败
**解决方案**:
```bash
# 检查nginx状态
docker-compose ps nginx
# 查看nginx日志
docker-compose logs nginx
```

### 诊断命令

```bash
# 检查服务状态
docker-compose ps

# 检查nginx配置
docker exec ynnx-nginx nginx -t

# 测试代理连接
curl -I "http://localhost:3668?tkn=tk-ynnx-llm"

# 查看nginx日志
docker-compose logs -f nginx
```

## 📚 相关文档

- [Docker Compose WebIDE集成方案](./DOCKER_COMPOSE_WEBIDE.md)
- [Web IDE 设置文档](./WEBIDE_SETUP.md)
- [故障排除脚本](./scripts/troubleshoot-webide-proxy.sh)

## 🎉 总结

通过nginx配置修复，VS Code扩展市场错误已被解决：

1. **错误消除**: 浏览器控制台不再显示JSON解析错误
2. **功能保持**: VS Code核心功能完全正常
3. **性能优化**: 避免了无效的外部请求
4. **内网友好**: 适合内网部署环境

修复后，您可以正常使用VS Code Web版本进行开发，而不会被控制台错误干扰。
