// iframe-resizer配置文件
// 这个文件将被注入到OpenVSCode中以支持iframe-resizer

(function() {
    'use strict';
    
    console.log('🔧 初始化iframe-resizer支持...');
    
    // 检查是否在iframe中
    if (window.parent !== window) {
        console.log('✅ 检测到iframe环境');
        
        // 动态加载iframe-resizer的子页面脚本
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/iframe-resizer@4.3.7/js/iframeResizer.contentWindow.min.js';
        script.onload = function() {
            console.log('✅ iframe-resizer子页面脚本加载成功');
            
            // 发送准备就绪消息
            if (window.parentIFrame) {
                window.parentIFrame.sendMessage({
                    type: 'vscode-ready',
                    timestamp: Date.now()
                });
            }
        };
        script.onerror = function() {
            console.error('❌ iframe-resizer子页面脚本加载失败');
        };
        document.head.appendChild(script);
        
        // 监听VS Code的加载完成事件
        const checkVSCodeReady = () => {
            // 检查VS Code是否已加载
            if (document.querySelector('.monaco-workbench') || 
                document.querySelector('.vs') ||
                document.title.includes('Visual Studio Code')) {
                console.log('🎉 VS Code已加载完成');
                
                // 通知父页面VS Code已准备就绪
                if (window.parentIFrame) {
                    window.parentIFrame.sendMessage({
                        type: 'vscode-loaded',
                        timestamp: Date.now()
                    });
                }
                
                return true;
            }
            return false;
        };
        
        // 定期检查VS Code加载状态
        const checkInterval = setInterval(() => {
            if (checkVSCodeReady()) {
                clearInterval(checkInterval);
            }
        }, 1000);
        
        // 10秒后停止检查
        setTimeout(() => {
            clearInterval(checkInterval);
        }, 10000);
        
    } else {
        console.log('ℹ️  不在iframe环境中');
    }
})();
