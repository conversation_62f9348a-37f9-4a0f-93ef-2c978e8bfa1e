# WebIDE 安全上下文问题修复

## 🚨 问题诊断

### 错误症状
1. **Iframe Sandbox警告**：
   ```
   An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing
   ```

2. **Crypto.subtle错误**：
   ```
   'crypto.subtle' is not available so webviews will not work. This is likely because the editor is not running in a secure context
   ```

### 🎯 根本原因
**非安全上下文(Non-Secure Context)**：
- ❌ `http://***********:3667` - 非安全上下文
- ✅ `https://***********:3667` - 安全上下文  
- ✅ `http://localhost:3667` - localhost例外

### 📊 功能影响对比

| 功能 | HTTP (***********) | HTTPS | localhost代理 |
|------|-------------------|-------|---------------|
| 基本编辑 | ✅ | ✅ | ✅ |
| 语法高亮 | ✅ | ✅ | ✅ |
| 代码补全 | ✅ | ✅ | ✅ |
| 文件管理 | ✅ | ✅ | ✅ |
| **Webviews** | ❌ | ✅ | ✅ |
| **crypto.subtle** | ❌ | ✅ | ✅ |
| 剪贴板API | ❌ | ✅ | ✅ |
| 扩展安装 | 受限 | ✅ | ✅ |

## 🔧 解决方案

### 方案1：配置HTTPS（推荐）⭐

#### 1.1 生成SSL证书
```bash
./scripts/setup-openvscode-https.sh
```

#### 1.2 启动HTTPS OpenVSCode
**Docker方式**：
```bash
docker run -it --init -p 3667:3000 \
  -v $(pwd)/ssl-certs:/certs \
  -e OPENVSCODE_SERVER_ROOT=/home/<USER>
  -e CONNECTION_TOKEN=tk-ynnx-llm \
  -e OPENVSCODE_USE_SSL=true \
  -e OPENVSCODE_SSL_CERT=/certs/cert.pem \
  -e OPENVSCODE_SSL_KEY=/certs/key.pem \
  gitpod/openvscode-server
```

**非Docker方式**：
```bash
openvscode-server --host *********** --port 3667 \
  --connection-token tk-ynnx-llm \
  --cert ./ssl-certs/cert.pem \
  --cert-key ./ssl-certs/key.pem
```

#### 1.3 更新配置
```bash
# 修改 .env 文件
VITE_WEBIDE_BASE_URL=https://***********:3667
```

#### 1.4 信任证书
1. 访问 `https://***********:3667`
2. 点击"高级" → "继续前往***********(不安全)"
3. 或将证书添加到浏览器受信任列表

---

### 方案2：localhost代理（简单）⭐

#### 2.1 配置nginx代理
```bash
./scripts/setup-localhost-proxy.sh
```

#### 2.2 启动代理
```bash
# 前台运行（调试用）
sudo nginx -c /tmp/openvscode-proxy.conf -g 'daemon off;'

# 后台运行
sudo nginx -c /tmp/openvscode-proxy.conf
```

#### 2.3 更新配置
```bash
# 修改 .env 文件
VITE_WEBIDE_BASE_URL=http://localhost:3667
```

#### 2.4 停止代理
```bash
sudo nginx -s stop
```

---

### 方案3：浏览器配置（临时）

#### Chrome配置
启动Chrome时添加参数：
```bash
google-chrome --disable-web-security --user-data-dir="/tmp/chrome_dev_test" --unsafely-treat-insecure-origin-as-secure=http://***********:3667
```

#### Firefox配置
1. 地址栏输入：`about:config`
2. 搜索：`security.tls.insecure_fallback_hosts`
3. 添加：`***********:3667`

⚠️ **注意**：这种方法降低了浏览器安全性，仅推荐开发环境使用。

## 🧪 测试步骤

### 验证安全上下文
在OpenVSCode控制台中运行：
```javascript
console.log('Secure Context:', window.isSecureContext);
console.log('Crypto.subtle:', !!window.crypto.subtle);
```

期望结果：
- ✅ `Secure Context: true`
- ✅ `Crypto.subtle: true`

### 验证Webviews功能
1. 安装一个使用webview的扩展
2. 检查扩展是否正常工作
3. 控制台不应有crypto.subtle错误

## 📋 故障排除

### 证书问题
**症状**：浏览器显示"不安全连接"
**解决**：
1. 检查证书文件权限
2. 重新生成证书
3. 手动信任证书

### 代理问题
**症状**：localhost:3667无法访问
**解决**：
1. 检查nginx是否运行：`ps aux | grep nginx`
2. 检查端口占用：`lsof -i :3667`
3. 查看nginx错误日志

### 权限问题
**症状**：nginx启动失败
**解决**：
1. 使用sudo运行nginx
2. 检查防火墙设置
3. 更换其他端口

## 🎯 推荐策略

### 开发环境
1. **首选**：localhost代理（简单、快速）
2. **备选**：自签名HTTPS（完整功能）

### 生产环境
1. **必须**：有效SSL证书的HTTPS
2. **配合**：反向代理（nginx/apache）

### 测试环境
1. **可选**：浏览器配置（快速测试）
2. **推荐**：localhost代理

## ✨ 最佳实践

1. **优先级**：HTTPS > localhost代理 > HTTP
2. **证书管理**：定期更新SSL证书
3. **监控**：定期检查安全上下文状态
4. **文档**：记录配置变更和证书信息

## 🔗 相关链接

- [MDN - Secure Contexts](https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts)
- [OpenVSCode Server Documentation](https://github.com/gitpod-io/openvscode-server)
- [nginx Proxy Configuration](https://nginx.org/en/docs/http/ngx_http_proxy_module.html) 