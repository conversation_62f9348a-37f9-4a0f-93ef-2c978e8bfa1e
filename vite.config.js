import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import legacy from '@vitejs/plugin-legacy'
import { visualizer } from 'rollup-plugin-visualizer';

// https://vite.dev/config/
export default defineConfig({
  // 为 Node.js 模块提供浏览器兼容的空实现
  define: {
    global: 'globalThis',
    'process.env.NODE_ENV': '"development"',
    // 内网部署：确保不会尝试访问外部资源
    __INTRANET_MODE__: true,
    __DISABLE_EXTERNAL_RESOURCES__: true,
    __OFFLINE_MODE__: true,
    // 禁用segment analytics在浏览器环境中的使用
    'process.env.SEGMENT_ANALYTICS_DISABLED': '"true"',
    // React 兼容性定义
    '__DEV__': 'false'
  },
  
  // 解析配置 - 排除Node.js专用包
  resolve: {
    alias: {
      // 为Node.js专用包提供空实现
      '@segment/analytics-node': 'virtual:segment-mock',
      'node-fetch': false,
    }
  },

  plugins: [
    react(),
    legacy({
      // 指定要支持的传统浏览器
      targets: [
        '> 0.5%',
        'last 2 versions',
        'Firefox ESR',
        'not dead',
        'not IE 11',
        'Chrome >= 60',
        'Firefox >= 60',
        'Safari >= 11',
        'Edge >= 79'
      ],
      // 现代浏览器的 polyfills
      modernPolyfills: true,
      // 使用 core-js 提供更好的 polyfill 支持
      additionalLegacyPolyfills: ['regenerator-runtime/runtime']
    }),
    visualizer({ open: false, filename: 'logs/stats.html' }),
    // 替换segment imports插件
    {
      name: 'replace-segment-imports',
             generateBundle(options, bundle) {
         for (const [, chunk] of Object.entries(bundle)) {
           if (chunk.type === 'chunk' && chunk.code) {
             // 替换所有segment imports
             chunk.code = chunk.code
               .replace(/import\s*"@segment\/analytics-node"\s*;?/g, '/* @segment/analytics-node removed */')
               .replace(/import\s*['"]@segment\/analytics-node['"]\s*;?/g, '/* @segment/analytics-node removed */')
               .replace(/from\s*['"]@segment\/analytics-node['"]/g, 'from "data:text/javascript,export default {};"');
           }
         }
      }
    }
  ],
  server: {
    host: '0.0.0.0',
    port: 5173,
    strictPort: false,
    open: false
  },
  build: {
    // 优化构建性能和兼容性
    cssTarget: 'chrome61',
    minify: 'terser', // 更好的压缩
    cssMinify: 'esbuild', // CSS 使用 esbuild 压缩
    modulePreload: {
      polyfill: true
    },
    sourcemap: false, // 生产环境关闭 sourcemap
    
    rollupOptions: {
      // 外部化 Node.js 模块，避免浏览器兼容性警告  
      external: ['@segment/analytics-node', 'node-fetch'],

      output: {
        // 更激进的代码分割策略
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            // React 相关
            if (id.includes('react-dom')) return 'vendor-react-dom';
            if (id.includes('react-router')) return 'vendor-router';
            if (id.includes('react-icons')) return 'vendor-icons';
            if (id.includes('react')) return 'vendor-react';

            // AI 相关库
            if (id.includes('@copilotkit') || id.includes('openai') || id.includes('@anthropic')) return 'vendor-ai';

            // 语法高亮和 Markdown
            if (id.includes('highlight.js') || id.includes('prism') || id.includes('react-markdown') ||
                id.includes('rehype') || id.includes('remark') || id.includes('refractor')) return 'vendor-syntax';

            // 图标和 UI 库
            if (id.includes('lucide-react') || id.includes('@headlessui') || id.includes('@react-aria')) return 'vendor-ui';

            // 工具库
            if (id.includes('axios') || id.includes('uuid') || id.includes('lodash')) return 'vendor-utils';

            // 其他第三方库
            return 'vendor';
          }

          if (id.includes('src/components')) {
            if (id.includes('AIAssistantSection')) return 'component-ai-assistant';
            if (id.includes('DocumentationSection')) return 'component-docs';
            if (id.includes('DownloadsSection')) return 'component-downloads';
            return 'components';
          }
        },
        
        // 文件命名优化
        chunkFileNames: () => {
          return `assets/[name]-[hash].js`;
        },
        
        assetFileNames: (assetInfo) => {
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `assets/images/[name]-[hash][extname]`;
          }
          if (/\.(css)$/i.test(assetInfo.name)) {
            return `assets/css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    
    // 提升chunk大小警告阈值，适应现代应用需求
    chunkSizeWarningLimit: 1500, // 提高到 1.5MB，现代应用的合理阈值
    
    // 压缩选项 - 优化更多
    terserOptions: {
      compress: {
        pure_funcs: ['console.log', 'console.info', 'console.warn'], // 移除更多console
        passes: 3, // 增加压缩次数
        unsafe: true,
        unsafe_comps: true,
        unsafe_Function: true,
        unsafe_math: true,
        unsafe_symbols: true,
        unsafe_methods: true,
        unsafe_proto: true,
        unsafe_regexp: true,
        unsafe_undefined: true,
        arguments: true,
        booleans_as_integers: true,
        collapse_vars: true,
        comparisons: true,
        computed_props: true,
        conditionals: true,
        dead_code: true,
        directives: true,
        drop_console: true,
        drop_debugger: true,
        ecma: 2020,
        evaluate: true,
        expression: false,
        global_defs: {},
        hoist_funs: false,
        hoist_props: true,
        hoist_vars: false,
        if_return: true,
        inline: 3,
        join_vars: true,
        keep_fargs: true,
        keep_fnames: false,
        keep_infinity: false,
        loops: true,
        negate_iife: true,
        properties: true,
        reduce_funcs: true,
        reduce_vars: true,
        sequences: true,
        side_effects: true,
        switches: true,
        top_retain: null,
        typeofs: true,
        unused: true,
        warnings: false
      },
      mangle: {
        safari10: true,
        toplevel: true
      },
      format: {
        comments: false
      }
    },
    
    // 资源内联阈值
    assetsInlineLimit: 4096
  },
  
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-icons/fa',
      'react-icons/hi',
      'lucide-react',
      'axios'
    ],
    exclude: [
      'openai', // 后端专用
      '@anthropic-ai/sdk', // 后端专用
      'express', // 后端专用
      'cors', // 后端专用
      'ldapjs', // 后端专用
      'node-fetch', // 后端专用
      'dotenv', // 后端专用
      'uuid', // 按需导入
      'concurrently', // 开发工具
      'react-router-dom', // 当前未使用
      '@segment/analytics-node', // Node.js 专用，避免浏览器构建警告
      '@segment/analytics-next'
    ],
    force: false,
    esbuildOptions: {
      target: 'es2020',
      // 移除未使用的代码
      treeShaking: true,
      // 压缩标识符
      minifyIdentifiers: true,
      // 压缩语法
      minifySyntax: true,
      // 压缩空格
      minifyWhitespace: true
    }
  },
  
  // CSS优化
  css: {
    devSourcemap: false,
    preprocessorOptions: {
      scss: {
        charset: false
      }
    }
  },

  // 静态资源处理 - 支持现代图片格式
  assetsInclude: ['**/*.webp', '**/*.avif', '**/*.jpg', '**/*.jpeg', '**/*.png'],
  
  // 性能优化
  esbuild: {
    drop: ['console', 'debugger'],
    legalComments: 'none'
  }
})
