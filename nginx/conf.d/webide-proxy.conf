# OpenVSCode代理配置
# 解决安全上下文问题，通过localhost:3668代理到***********:3667

# 上游OpenVSCode服务器
upstream openvscode_backend {
    server ***********:3667;
    keepalive 32;
}

# 代理服务器配置
server {
    listen 3667;
    server_name localhost;

    # 增加缓冲区大小处理WebSocket和大文件
    client_max_body_size 100M;
    proxy_buffering off;
    proxy_request_buffering off;

    # 日志配置
    access_log /var/log/nginx/webide-access.log;
    error_log /var/log/nginx/webide-error.log warn;

    # 拦截扩展市场请求，返回空响应避免错误
    location ~* /vscode/gallery/ {
        access_log off;
        return 404 '{"error": "Extension marketplace disabled"}';
        add_header Content-Type application/json;
    }

    # 主要代理配置
    location / {
        # 代理到OpenVSCode服务器
        proxy_pass http://openvscode_backend;

        # 基本代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 超时配置 - OpenVSCode可能需要较长连接时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # 保持连接
        proxy_set_header Connection "keep-alive";

        # 禁用缓冲以支持实时通信
        proxy_buffering off;
        proxy_cache off;

        # 支持大文件上传
        client_max_body_size 100M;
    }

    # 静态资源优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://openvscode_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 静态资源缓存
        expires 1h;
        add_header Cache-Control "public, immutable";
        
        # 压缩
        gzip on;
        gzip_types text/css application/javascript application/json;
    }

    # WebSocket特殊处理路径
    location ~* /(websocket|socket\.io|ws)/ {
        proxy_pass http://openvscode_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket超时配置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "OpenVSCode Proxy OK\n";
        add_header Content-Type text/plain;
    }
}

# WebSocket连接升级映射
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
} 