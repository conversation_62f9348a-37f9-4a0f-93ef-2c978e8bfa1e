{"name": "ynnx-ai-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "NODE_ENV=development vite", "dev:host": "vite --host 0.0.0.0", "build": "NODE_ENV=production vite build --mode production && node scripts/optimize-critical-path.js && node scripts/replace-external-links.cjs", "build:analyze": "npm run build && node scripts/analyze-build.cjs", "build:staging": "NODE_ENV=staging vite build", "build:intranet": "node scripts/build-intranet.cjs", "verify:intranet": "node scripts/verify-intranet-deployment.cjs", "lint": "eslint .", "preview": "vite preview", "preview:host": "vite preview --host 0.0.0.0", "ldap": "NODE_ENV=development node scripts/start-ldap.js", "dev:full": "concurrently \"npm run dev\" \"npm run ldap\"", "dev:network": "concurrently \"npm run dev:host\" \"npm run ldap\"", "config:check": "node scripts/check-hardcoded-config.cjs", "config:validate": "node scripts/validate-env-config.cjs", "config:setup": "cp env.example .env && echo '✅ 已创建 .env 文件，请根据实际环境修改配置值'", "intranet:check": "node scripts/check-external-dependencies.cjs", "intranet:prepare": "node scripts/prepare-intranet-deployment.cjs", "intranet:localize": "node scripts/localize-external-resources.cjs", "intranet:restore": "node scripts/restore-original-files.cjs", "intranet:restore-localized": "node scripts/restore-localized-resources.cjs", "check:external": "npm run intranet:check", "optimize": "node scripts/run-all-optimizations.js", "optimize:tailwind": "node scripts/optimize-tailwind.cjs", "optimize:images": "node scripts/optimize-images.cjs", "optimize:build": "npm run optimize && npm run build"}, "dependencies": {"@anthropic-ai/sdk": "^0.17.0", "@copilotkit/runtime": "^1.9.1", "axios": "^1.9.0", "concurrently": "^8.2.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "highlight.js": "^11.11.1", "iframe-resizer": "^5.4.7", "iframe-resizer-react": "^5.1.5", "ldapjs": "^3.0.7", "lucide-react": "^0.511.0", "node-fetch": "^3.3.2", "openai": "^4.28.0", "prismjs": "1.30.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-markdown": "10.1.0", "react-router-dom": "^7.6.0", "rehype-highlight": "7.0.2", "rehype-raw": "7.0.0", "remark-gfm": "4.0.1", "uuid": "^9.0.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "core-js": "^3.43.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "rollup-plugin-visualizer": "6.0.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}, "browserslist": ["> 0.5%", "last 2 versions", "Firefox ESR", "not dead", "not IE 11"]}