# Docker Compose WebIDE集成方案

## 🎯 方案概述

通过Docker Compose集成nginx代理，解决OpenVSCode的安全上下文问题，实现完整的WebIDE功能。

### 📊 架构图

```
浏览器 → localhost:3668 → nginx代理 → ***********:3667 → OpenVSCode
       ↓
    安全上下文 ✅
    crypto.subtle ✅
    webviews功能 ✅
```

## 🔧 配置文件结构

### 1. docker-compose.yml 扩展
```yaml
services:
  nginx:
    ports:
      - "80:80"        # Web应用
      - "3668:3667"    # OpenVSCode代理端口
    volumes:
      - ./nginx/conf.d/webide-proxy.conf:/etc/nginx/conf.d/webide-proxy.conf:ro
```

### 2. nginx代理配置
- 文件：`nginx/conf.d/webide-proxy.conf`
- 功能：将localhost:3667代理到***********:3667
- 特性：WebSocket支持、大文件上传、静态资源优化

### 3. 环境变量配置
```bash
VITE_WEBIDE_BASE_URL=http://localhost:3668
VITE_WEBIDE_TOKEN=tk-ynnx-llm
VITE_WEBIDE_TOKEN_PARAM=tkn
```

## 🚀 部署方式

### 生产部署
```bash
# 完整部署（推荐）
./scripts/deploy-with-webide-proxy.sh

# 手动部署
docker-compose build
docker-compose up -d
```

### 开发环境
```bash
# 开发环境（自动配置）
./scripts/dev-with-webide-proxy.sh

# 手动开发
npm run dev
```

## 🔍 故障排除

### 快速诊断
```bash
./scripts/troubleshoot-webide-proxy.sh
```

### 常见问题

#### 1. 代理无法访问
**症状**：localhost:3667无响应
**解决**：
```bash
docker-compose restart nginx
docker-compose logs nginx
```

#### 2. OpenVSCode连接失败
**症状**：代理正常但后端连接失败
**解决**：
```bash
# 检查OpenVSCode服务器
curl http://***********:3667
# 重启OpenVSCode服务器
```

#### 3. 安全上下文仍然异常
**症状**：crypto.subtle仍不可用
**解决**：
```bash
# 确认配置
grep VITE_WEBIDE_BASE_URL .env
# 应该显示：VITE_WEBIDE_BASE_URL=http://localhost:3667
```

## 📋 管理命令

### Docker Compose管理
```bash
# 启动所有服务
docker-compose up -d

# 重启nginx代理
docker-compose restart nginx

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f nginx

# 停止服务
docker-compose down
```

### 开发环境管理
```bash
# 启动开发代理
docker run -d --name ynnx-webide-proxy -p 3667:3667 \
  -v /tmp/dev-webide-proxy.conf:/etc/nginx/nginx.conf:ro \
  nginx:alpine

# 停止开发代理
docker stop ynnx-webide-proxy && docker rm ynnx-webide-proxy

# 查看代理日志
docker logs ynnx-webide-proxy
```

## 🧪 测试验证

### 1. 服务连通性测试
```bash
# 测试OpenVSCode原始服务
curl http://***********:3667

# 测试代理服务
curl http://localhost:3667/health

# 测试完整代理
curl http://localhost:3667
```

### 2. 安全上下文验证
在浏览器控制台中执行：
```javascript
console.log('Secure Context:', window.isSecureContext);
console.log('Crypto.subtle:', !!window.crypto.subtle);
```

期望结果：
- `Secure Context: true` ✅
- `Crypto.subtle: true` ✅

### 3. WebIDE功能测试
1. 访问主应用并登录
2. 进入Web IDE部分
3. 点击"新窗口打开"
4. 验证OpenVSCode完整功能

## 📊 性能优化

### nginx代理优化
- **连接保持**：`keepalive 32`
- **缓冲优化**：`proxy_buffering off`
- **超时配置**：读写超时300秒
- **静态资源缓存**：1小时缓存
- **Gzip压缩**：CSS/JS/JSON压缩

### WebSocket支持
- **协议升级**：HTTP/1.1到WebSocket
- **连接升级映射**：自动处理upgrade头
- **长连接支持**：24小时连接超时

## 🔐 安全考虑

### 代理安全
- **仅本地访问**：代理只监听localhost
- **上游验证**：检查上游服务器可用性
- **请求头转发**：保持必要的认证信息

### 网络隔离
- **Docker网络**：容器间通信隔离
- **端口映射**：最小化暴露端口
- **健康检查**：定期检查服务状态

## 🎯 最佳实践

### 部署策略
1. **开发环境**：使用`dev-with-webide-proxy.sh`
2. **测试环境**：使用docker-compose
3. **生产环境**：使用`deploy-with-webide-proxy.sh`

### 监控建议
1. **日志监控**：定期检查nginx代理日志
2. **健康检查**：监控/health端点
3. **性能监控**：WebSocket连接数和延迟

### 维护计划
1. **定期重启**：重启代理服务清理连接
2. **日志轮转**：nginx日志定期清理
3. **配置备份**：备份nginx代理配置

## 🔗 相关文档

- [WEBIDE_SECURITY_CONTEXT_FIX.md](./WEBIDE_SECURITY_CONTEXT_FIX.md) - 安全上下文问题详解
- [WEBIDE_CORS_FIX.md](./WEBIDE_CORS_FIX.md) - CORS问题修复
- [Docker Compose官方文档](https://docs.docker.com/compose/)
- [nginx代理配置](https://nginx.org/en/docs/http/ngx_http_proxy_module.html)

---

**✨ 这个方案完美解决了WebIDE的安全上下文问题，同时为生产部署提供了完整的Docker Compose集成！** 