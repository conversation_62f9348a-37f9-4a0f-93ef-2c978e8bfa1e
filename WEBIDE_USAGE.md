# WebIDE 使用指南

## 🚀 快速开始

### 1. 确保OpenVSCode服务器正在运行
您应该能够通过以下URL直接访问：
```
http://***********:3667?tkn=tk-ynnx-llm
```

### 2. 登录平台
- 访问 `http://localhost:5174` (或您的开发服务器地址)
- 使用您的账号登录

### 3. 访问WebIDE
- 在导航栏中点击 "Web IDE" 链接
- 等待IDE加载完成

## 🔧 配置说明

### 环境变量
在您的 `.env` 文件中确保以下配置正确：

```env
VITE_WEBIDE_BASE_URL=http://***********:3667
VITE_WEBIDE_TOKEN=tk-ynnx-llm
VITE_WEBIDE_TOKEN_PARAM=tkn
```

### 自定义配置
如果您的OpenVSCode服务器使用不同的配置，可以修改：

- `VITE_WEBIDE_BASE_URL`: 服务器地址
- `VITE_WEBIDE_TOKEN`: 认证token
- `VITE_WEBIDE_TOKEN_PARAM`: token参数名称

## 🛠️ 功能特性

### 控制按钮
- **刷新**: 重新加载IDE界面
- **全屏**: 切换全屏模式
- **新窗口打开**: 在新窗口中打开IDE
- **测试URL**: 测试IDE的URL是否可访问

### 调试功能
- 浏览器控制台中查看详细的调试信息
- 自动URL可访问性检查
- 错误状态的详细故障排除建议

## 🐛 故障排除

### 如果显示403错误
1. 确认OpenVSCode服务器正在运行
2. 在新窗口中测试URL是否可访问
3. 检查浏览器控制台的详细错误信息

### 如果iframe无法加载
1. 点击 "测试URL" 按钮验证URL
2. 尝试 "刷新" 按钮重新加载
3. 使用 "新窗口打开" 在独立窗口中使用

### 调试步骤
1. 打开浏览器开发者工具
2. 查看控制台中的WebIDE相关日志
3. 检查网络标签中的HTTP请求状态

## 📋 常见问题

### Q: 为什么需要登录？
A: WebIDE是高级功能，需要验证用户身份才能使用。

### Q: 可以更改服务器地址吗？
A: 是的，在 `.env` 文件中修改 `VITE_WEBIDE_BASE_URL` 即可。

### Q: 如何更改认证token？
A: 修改 `.env` 文件中的 `VITE_WEBIDE_TOKEN` 和 `VITE_WEBIDE_TOKEN_PARAM`。

### Q: 全屏模式怎么退出？
A: 按 ESC 键或点击 "退出全屏" 按钮。

## 🎯 预期行为

当一切正常时，您应该看到：
- ✅ 控制台显示WebIDE配置信息
- ✅ iframe成功加载OpenVSCode界面
- ✅ 可以看到VS Code的文件浏览器和编辑器
- ✅ 可以创建和编辑文件

## 📞 获取帮助

如果遇到问题，请：
1. 检查此文档的故障排除部分
2. 查看浏览器控制台的错误信息
3. 联系技术支持团队 